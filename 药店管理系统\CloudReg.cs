﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common;
using YdPublicFunction;
using YdPublicFunction.Mdl;

namespace 药店管理系统
{
    public partial class CloudReg : Common.BaseForm.BaseChild
    {
        public CloudReg()
        {
            InitializeComponent();
        }
        Common.Register reg = new Register();
        private void CloudReg_Load(object sender, EventArgs e)
        {
            reg.Domain = RegDomain.HKEY_CURRENT_USER;
            reg.SubKey = "SOFTWARE\\ztsoft\\YdSystem\\";
            if (!reg.IsSubKeyExist() || !reg.IsRegeditKeyExist("Yd_Code"))
            {
                TxtYd_Tele.Text = "";
                TxtName.Text = "";
            }
            else
            {
                // 优先读取新的字段名
                if (reg.IsRegeditKeyExist("Yd_Tele"))
                {
                    TxtYd_Tele.Text = reg.ReadRegeditKey("Yd_Tele").ToString();
                }
                else
                {
                    TxtYd_Tele.Text = "";
                }

                if (reg.IsRegeditKeyExist("Yd_Name"))
                {
                    TxtName.Text = reg.ReadRegeditKey("Yd_Name").ToString();
                }
                else
                {
                    TxtName.Text = "";
                }
            }
            TxtYd_Tele.Select();
            TxtName.Enabled = false;
        }


        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (CustomControl.Func.NotAllowEmpty(TxtYd_Tele)) return;

            MdlGetDbConfigByTeleIn inputModel = new MdlGetDbConfigByTeleIn
            {
                Yd_Tele = TxtYd_Tele.Text
            };

            string msg = "";
            MdlGetDbConfigByTeleOut outputModel = null;

            //调用接口，获取药店数据库连接信息
            if (CentralApi.GetDbConfigByTele(inputModel, ref msg, ref outputModel) == 0 && outputModel != null)
            {
                // 保存电话、药店代码、药店名称到注册表
                reg.WriteRegeditKey("Yd_Tele", TxtYd_Tele.Text, RegValueKind.String);
                reg.WriteRegeditKey("Yd_Code", outputModel.Yd_Code, RegValueKind.String);
                reg.WriteRegeditKey("Yd_Name", outputModel.Yd_Name, RegValueKind.String);

                MessageBox.Show("认证成功");
                TxtName.Text = outputModel.Yd_Name;
            }
            else
            {
                MessageBox.Show($"认证失败：{msg}");
                TxtYd_Tele.Focus();
            }

            this.DialogResult = DialogResult.OK;
        }


    }
}
