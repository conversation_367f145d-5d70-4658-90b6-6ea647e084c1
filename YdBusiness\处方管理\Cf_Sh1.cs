using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Common.BaseForm;
using YdPublicFunction;
using YdVar;

namespace YdBusiness
{
    public partial class Cf_Sh1 : Common.BaseForm.BaseDict1
    {
        private BLL.BllYk_Ck1 _bllYkCk1 = new BllYk_Ck1();
        string cf_state = "";
        public Cf_Sh1()
        {
            InitializeComponent();
        }
        private void Cf_Sh1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            FormInit();
            DataInit();
            myGrid1.Select();
        }
        #region 初始化
        private void FormInit()
        {
            doubleDateEdit1.CustomFormat = "yyyy-MM-dd";
            doubleDateEdit1.DisplayFormat = "yyyy-MM-dd";
            doubleDateEdit1.EditFormat = "yyyy-MM-dd";
            doubleDateEdit1.SelectedIndex = 5;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("处方状态", "Cf_State", 100, "中", "", false);
            myGrid1.Init_Column("审核时间", "Sf_Date", 120, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("截图", "ImageData", 60, "中", "", false);
            myGrid1.Init_Column("处方编号", "Cf_Code", 120, "中", "", false);
            myGrid1.Init_Column("处方医师", "Cf_Cfys", 120, "左", "", false);
            myGrid1.Init_Column("开具单位", "Cf_Dw", 120, "左", "", false);
            myGrid1.Init_Column("审核备注", "Cf_Memo", 120, "中", "", false);
            myGrid1.Splits[0].DisplayColumns["Cf_State"].FetchStyle = true;
            myGrid1.Splits[0].DisplayColumns["ImageData"].FetchStyle = true;
            myGrid1.ColumnFooters = true;
            myGrid1.AllowSort = true;
            myGrid1.FetchRowStyles = true;
            myGrid1.FetchCellStyle += myGrid1_FetchCellStyle;
        }
        #endregion

        #region 自定义函数
        private void DataInit()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("(Sf_Date is null or Sf_Date between '");
            strSql.Append(DateTime.Parse(doubleDateEdit1.StartValue.ToString()).ToString("yyy-MM-dd"));
            strSql.Append("' And '");
            strSql.Append(DateTime.Parse(doubleDateEdit1.EndValue.ToString()).ToString("yyy-MM-dd 23:59:59"));
            strSql.Append("')");
            strSql.Append($" And Sfys_Code='{Var.JsrCode}'");
            if (string.IsNullOrEmpty(cf_state))
            {
                strSql.Append($" And Cf_State='{cf_state}'");
            }
            base.MyTable = _bllYkCk1.GetList(strSql.ToString()).Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Ck_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = base.MyTable));
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Sf_Date desc";
            DataSum();
        }
        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            XsCk2 vform = new XsCk2(base.Insert, base.MyRow, base.MyTable);
            vform.Tag = base.MyRow["Ck_Code"];
            vform.ZbTransmitTxt = base.MyTransmitTxt;
            base.AddTabControl(vform, "处方审核明细-" + (base.MyRow["Ck_Code"].ToString() == "" ? "新出库" : base.MyRow["Ck_Code"].ToString()), YdResources.C_Resources.GetImage16("销售出库"));
        }
        private void DataSum()
        {
            LblTotal.BeginInvoke(new Action(() => this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString()));
        }
        #endregion

        #region 事件
        private void CmdQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataInit();
        }
        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            char[] split = { ' ' };
            string filter = "Gk_Name+Ck_Code+Czy_Name+Pay_Type+Cf_Dw+Cf_CfYs";
            string strFilter = "";
            foreach (string substr in TxtFilter.Text.Replace("*", "[*]").Replace("%", "[%]").Split(split))
            {
                strFilter = strFilter + filter + " like '*" + substr + "*' And ";
            }
            strFilter = strFilter.Substring(0, strFilter.Length - 5);
            MyView.RowFilter = strFilter;
            DataSum();
        }

        private void myGrid1_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1.Win.C1TrueDBGrid.C1TrueDBGrid grid = (C1.Win.C1TrueDBGrid.C1TrueDBGrid)sender;
            switch (e.Column.DataColumn.DataField)
            {
                case "ImageData":
                    object imageData = grid.Columns["ImageData"].CellValue(e.Row);
                    if (imageData != null && imageData != DBNull.Value && ((byte[])imageData).Length > 0)
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.默认16;
                    }
                    e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly;
                    break;
            }
        }

        #endregion

        private void CmdSh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            cf_state = "";
            DataInit();
        }

        private void CmdShPass_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {

        }

        private void CmdNotPass_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {

        }

        private void CmdWaitSh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {

        }
    }
}
