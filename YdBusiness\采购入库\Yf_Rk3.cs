﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Common.BaseForm;
using Model;

namespace YdBusiness
{
    public partial class Yf_Rk3 : Common.BaseForm.DoubleFormRK2
    {
        private string Zb_Code;
        private BllYk_Rk2 _bllRk2 = new BllYk_Rk2();
        private BllZd_Yp3 _bllZdYp3 = new BllZd_Yp3();
        public Yf_Rk3(string Zb_Code, bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            this.Zb_Code = Zb_Code;
            base.Insert = insert;
            base.SubItemRow = row;
            base.MyTable = table;
            comboYp1.GotFocus += new System.EventHandler(base.InputEn);
            comboYpPh1.GotFocus += new System.EventHandler(base.InputEn);
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Yf_Rk3_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.SubItemRow);
        }

        #region 初始化函数
        private void FormInit()
        {
            comboYp1.Init("");
            TxtGyzz.Enabled = false;   
            TxtCd.Enabled = false;
            TxtGg.Enabled = false;
            TxtJxName.Enabled = false;
            numCfbl.Enabled = false;
            NumRkMoney.Enabled = false;

            DateYxq.EditFormat = "yyyy-MM-dd";
            DateYxq.CustomFormat = "yyyy-MM-dd";
            DateYxq.DisplayFormat = "yyyy-MM-dd";

            dateScrq.EditFormat = "yyyy-MM-dd";
            dateScrq.CustomFormat = "yyyy-MM-dd";
            dateScrq.DisplayFormat = "yyyy-MM-dd";

            NumRkSl.CustomFormat = "######0.####";
            NumCgj.CustomFormat = "######0.00####";
            numCfbl.CustomFormat = "######0.######";
            NumRkMoney.CustomFormat = "######0.00####";
        
            panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        #endregion

         #region 自定义函数
        private void DataClear()
        {
            base.Insert = true;
            comboYp1.Enabled = true;
            comboYpPh1.Enabled = true;
            comboYp1.SelectedIndex = -1;
            TxtGyzz.Text = "";
            TxtCd.Text = "";
            TxtGg.Text = "";
            TxtJxName.Text = "";
            numCfbl.Value = DBNull.Value;
            comboYpPh1.SelectedIndex = -1;
            comboYpPh1.Text = "";
            dateScrq.Value = DBNull.Value;
            DateYxq.Value = DBNull.Value;
            NumRkSl.Value = 0;
            NumCgj.Value = 0;    
            NumRkMoney.Value = 0;
            TxtMemo.Text = "";
            comboYp1.Select();
        }
        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            comboYp1.Enabled = false;
            comboYpPh1.Enabled = false;
            comboYp1.SelectedValue = row["Xl_Code"] + "";
            comboYpPh1.SelectedValue = row["Yp_Code"] + "";
            comboYpPh1.Text = row["Yp_Scph"] + "";

            dateScrq.Value = Convert.ToDateTime(row ["Yp_Scdate1"]+"");
            DateYxq.Value = Convert.ToDateTime(row["Yp_Scdate2"] + "");

            NumRkSl.Value = decimal.Parse(row["Rk_Sl"] + "");
            NumCgj.Value = decimal.Parse(row["Rk_Dj"] + "");
            NumRkMoney.Value = decimal.Parse(row["Rk_Money"] + "");
            NumRkSl.Select();
        }

        private bool DataCheck()
        {
            if (CustomControl.Func.NotAllowEmpty(comboYp1)) return false;
            if (string.IsNullOrWhiteSpace(comboYpPh1.Text))
            {
                MessageBox.Show("药品批号不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                comboYpPh1.Select();
                return false;
            }

            if (CustomControl.Func.NotAllowEmpty(DateYxq)) return false;
            if (CustomControl.Func.NotAllowEmpty(dateScrq)) return false;

            if (DateTime.Parse(DateYxq.Value.ToString()) < DateTime.Now)
            {
                MessageBox.Show("药品有效期不能小于当前时间!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                DateYxq.Select();
                return false;
            }

            if (CustomControl.Func.NotAllowEmpty(NumRkSl)) return false;
            if (Convert.ToDecimal(NumRkSl.Value) <= 0)
            {
                MessageBox.Show("入库数量必须大于0！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumRkSl.Select();
                return false;
            }

            if (CustomControl.Func.NotAllowEmpty(NumCgj)) return false;
            if (Convert.ToDecimal(NumCgj.Value) < 0)
            {
                MessageBox.Show("采购单价不能为负数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumCgj.Select();
                return false;
            }

            return true;
        }

        private void DataAdd()
        {
           
            if (_bllRk2.GetRecordCount($"  Xl_Code='{comboYp1.SelectedValue+""}'  And Rk_Code='{Zb_Code}' And Yp_Scph='{comboYpPh1.Text+""}'") > 0)
            {
                MessageBox.Show("入库单已存在此药品，请检查!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                comboYpPh1.Select();
                return;
            }
            base.SubItemRow = base.MyTable.NewRow();
            SubItemRow["Rk_Code"] = Zb_Code;   //入库编码
            SubItemRow["Rk_Sl"] = NumRkSl.Value;
            SubItemRow["Rk_Dj"] = NumCgj.Value;
            SubItemRow["Rk_Money"] = Common.MathFormula.Multiply(NumRkSl.Value, NumCgj.Value);
            SubItemRow["Rk_Memo"] = TxtMemo.Text;
            SubItemRow["Yp_ScPh"] = comboYpPh1.Text;
            SubItemRow["Yp_ScDate1"] = dateScrq.Value;
            SubItemRow["Yp_ScDate2"] = DateYxq.Value;
            SubItemRow["Xl_Code"] = comboYp1.SelectedValue;
            SubItemRow["Yp_Name"] = comboYp1.Text;
            SubItemRow["Yp_Zjgg"] = TxtGg.Text;
            SubItemRow["Yp_Pzwh"] = TxtGyzz.Text;
            SubItemRow["Yp_Scqy"] = TxtCd.Text;
            SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue;
            SubItemRow["Yp_Bzzhb"] = comboYp1.Columns["Yp_Bzzhb"].Value;
            SubItemRow["Drug_Identification_Code"] = comboYp1.Columns["Drug_Identification_Code"].Value;
         
            //数据保存
            try
            {
                Model.MdlYk_Rk2  mdlYfRk2 = Common.DataTableToList.ToModel<MdlYk_Rk2>(SubItemRow);
                string Rk_Id = "-1";
                _bllRk2.Add(mdlYfRk2, ref Rk_Id);
                SubItemRow["Rk_Id"] = Convert.ToInt32(Rk_Id);
                base.MyTable.Rows.Add(base.SubItemRow);
                base.SubItemRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("数据增加成功");
                comboYp1.Focus();
            }

            DataClear();

        }

        private void DataEdit()
        {
            Model.MdlYk_Rk2 mdlRk2 = _bllRk2.GetModel((int)SubItemRow["Rk_Id"]);
           
            SubItemRow["Rk_Code"] = Zb_Code;   //入库编码
            SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue;
            SubItemRow["Rk_Sl"] = NumRkSl.Value;
            SubItemRow["Rk_Dj"] = NumCgj.Value;

            SubItemRow["Rk_Money"] = Common.MathFormula.Multiply(NumRkSl.Value, NumCgj.Value);
            SubItemRow["Rk_Memo"] = TxtMemo.Text;
            SubItemRow["Yp_ScPh"] = comboYpPh1.Text;
            SubItemRow["Yp_ScDate1"] = dateScrq.Value;
            SubItemRow["Yp_ScDate2"] = DateYxq.Value;
            SubItemRow["Xl_Code"] = comboYp1.SelectedValue;
            SubItemRow["Yp_Name"] = comboYp1.Text;
            SubItemRow["Yp_Zjgg"] = TxtGg.Text;
            SubItemRow["Yp_Pzwh"] = TxtGyzz.Text;
            SubItemRow["Yp_Scqy"] = TxtCd.Text;
            SubItemRow["Yp_Bzzhb"] = comboYp1.Columns["Yp_Bzzhb"].Value;
            SubItemRow["Drug_Identification_Code"] = comboYp1.Columns["Drug_Identification_Code"].Value;

            //数据保存
            try
            {
                Common.DataTableToList.ToModel(SubItemRow, mdlRk2);
                _bllRk2.Update(mdlRk2);
                base.SubItemRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                comboYp1.Focus();
            }
        }

        private void ComputeRkMoney()
        {
            if (NumRkSl.Value == DBNull.Value) return;
            if (NumCgj.Value == DBNull.Value) return;
            NumRkMoney.Value = (decimal)NumRkSl.Value * (decimal)NumCgj.Value;

        }
    
        #endregion

        #region 控件动作
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!DataCheck()) return;
            if (Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }
        private void comboYp1_RowChange(object sender, EventArgs e)
        {
            TxtGyzz.Text = comboYp1.Columns["Yp_Pzwh"].Value + "";
            TxtCd.Text = comboYp1.Columns["Yp_Scqy"].Value + "";
            TxtGg.Text = comboYp1.Columns["Yp_Zjgg"].Value + "";
            TxtJxName.Text = comboYp1.Columns["Yp_Jx"].Value + "";
            numCfbl.Value = comboYp1.Columns["Yp_Bzzhb"].Value + "";
            lblDw.Text = comboYp1.Columns["Yp_Zjdw"].Value + "";
            lblDw1.Text = "/" + comboYp1.Columns["Yp_Zjdw"].Value + "";

            comboYpPh1.Init($"  y3.Xl_Code='{comboYp1.SelectedValue+""}' And Yp_ScDate2>Getdate()");

            DataTable yfRkTable = _bllRk2.GetFirstYpDjList(comboYp1.SelectedValue + "");
            if (yfRkTable.Rows.Count > 0)
            {
                NumCgj.Value = Convert.ToDouble(yfRkTable.Rows[0]["Rk_Dj"]);
             
            }
            else
            {
                NumCgj.Value = 0;
            }
        }
        private void comboYpPh1_RowChange(object sender, EventArgs e)
        {
            if (comboYpPh1.WillChangeToValue + "" != "")
            {
                dateScrq.Value = comboYpPh1.Columns["Yp_ScDate1"].Value;
                DateYxq.Value = comboYpPh1.Columns["Yp_ScDate2"].Value;
            }
        }
        private void NumRkSl_ValueChanged(object sender, EventArgs e)
        {
            ComputeRkMoney();
        }
        private void NumCgj_ValueChanged(object sender, EventArgs e)
        {
            ComputeRkMoney();
        }

        #endregion

    }
}
