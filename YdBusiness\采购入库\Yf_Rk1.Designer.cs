﻿
namespace YdBusiness
{
    partial class Yf_Rk1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Yf_Rk1));
            this.myGrid1 = new CustomControl.MyGrid();
            this.c1CommandLink10 = new C1.Win.C1Command.C1CommandLink();
            this.Control_Lbl = new C1.Win.C1Command.C1CommandControl();
            this.LblTotal = new System.Windows.Forms.Label();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.CmdQuery = new C1.Win.C1Command.C1Command();
            this.c1CommandControl3 = new C1.Win.C1Command.C1CommandControl();
            this.doubleDateEdit1 = new CustomControl.DoubleDateEdit();
            this.Cmd_Del = new C1.Win.C1Command.C1Command();
            this.Cmd_Add = new C1.Win.C1Command.C1Command();
            this.c1CommandControl1 = new C1.Win.C1Command.C1CommandControl();
            this.TxtFilter = new CustomControl.MyTextBox();
            this.c1CommandControl2 = new C1.Win.C1Command.C1CommandControl();
            this.c1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink5 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink9 = new C1.Win.C1Command.C1CommandLink();
            this.c1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.c1ToolBar1.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.Images.SetKeyName(0, "增加.png");
            this.imageList1.Images.SetKeyName(1, "删除.png");
            this.imageList1.Images.SetKeyName(2, "打印.png");
            this.imageList1.Images.SetKeyName(3, "导入.png");
            this.imageList1.Images.SetKeyName(4, "导出.png");
            this.imageList1.Images.SetKeyName(5, "上移.png");
            this.imageList1.Images.SetKeyName(6, "下移.png");
            this.imageList1.Images.SetKeyName(7, "启用.png");
            this.imageList1.Images.SetKeyName(8, "停用.png");
            this.imageList1.Images.SetKeyName(9, "刷新.png");
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 50);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(992, 400);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 1;
            this.myGrid1.Xmlpath = null;
            // 
            // c1CommandLink10
            // 
            this.c1CommandLink10.Command = this.Control_Lbl;
            this.c1CommandLink10.Delimiter = true;
            this.c1CommandLink10.SortOrder = 4;
            // 
            // Control_Lbl
            // 
            this.Control_Lbl.Control = this.LblTotal;
            this.Control_Lbl.Name = "Control_Lbl";
            this.Control_Lbl.ShortcutText = "";
            // 
            // LblTotal
            // 
            this.LblTotal.AutoSize = true;
            this.LblTotal.BackColor = System.Drawing.Color.Transparent;
            this.LblTotal.Location = new System.Drawing.Point(482, 15);
            this.LblTotal.Name = "LblTotal";
            this.LblTotal.Size = new System.Drawing.Size(35, 14);
            this.LblTotal.TabIndex = 4;
            this.LblTotal.Text = "∑=0";
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.Control_Lbl);
            this.c1CommandHolder1.Commands.Add(this.CmdQuery);
            this.c1CommandHolder1.Commands.Add(this.c1CommandControl3);
            this.c1CommandHolder1.Commands.Add(this.Cmd_Del);
            this.c1CommandHolder1.Commands.Add(this.Cmd_Add);
            this.c1CommandHolder1.Commands.Add(this.c1CommandControl1);
            this.c1CommandHolder1.Commands.Add(this.c1CommandControl2);
            this.c1CommandHolder1.Owner = this;
            // 
            // CmdQuery
            // 
            this.CmdQuery.Image = global::YdBusiness.Properties.Resources.查询;
            this.CmdQuery.Name = "CmdQuery";
            this.CmdQuery.ShortcutText = "";
            this.CmdQuery.Text = "查询";
            this.CmdQuery.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdQuery_Click);
            // 
            // c1CommandControl3
            // 
            this.c1CommandControl3.Control = this.doubleDateEdit1;
            this.c1CommandControl3.Name = "c1CommandControl3";
            this.c1CommandControl3.ShortcutText = "";
            // 
            // doubleDateEdit1
            // 
            this.doubleDateEdit1.BackColor = System.Drawing.Color.Transparent;
            this.doubleDateEdit1.Captain = "入库日期";
            this.doubleDateEdit1.CaptainWidth = 60F;
            this.doubleDateEdit1.ControlFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.doubleDateEdit1.Location = new System.Drawing.Point(79, 12);
            this.doubleDateEdit1.MaximumSize = new System.Drawing.Size(1166666624, 27);
            this.doubleDateEdit1.MinimumSize = new System.Drawing.Size(0, 20);
            this.doubleDateEdit1.Name = "doubleDateEdit1";
            this.doubleDateEdit1.Size = new System.Drawing.Size(358, 20);
            this.doubleDateEdit1.TabIndex = 6;
            // 
            // Cmd_Del
            // 
            this.Cmd_Del.Image = global::YdBusiness.Properties.Resources.删除;
            this.Cmd_Del.Name = "Cmd_Del";
            this.Cmd_Del.ShortcutText = "";
            this.Cmd_Del.Text = "删除";
            this.Cmd_Del.Click += new C1.Win.C1Command.ClickEventHandler(this.Cmd_Del_Click);
            // 
            // Cmd_Add
            // 
            this.Cmd_Add.Image = global::YdBusiness.Properties.Resources.增加;
            this.Cmd_Add.Name = "Cmd_Add";
            this.Cmd_Add.ShortcutText = "";
            this.Cmd_Add.Text = "增加";
            this.Cmd_Add.Click += new C1.Win.C1Command.ClickEventHandler(this.Cmd_Add_Click);
            // 
            // c1CommandControl1
            // 
            this.c1CommandControl1.Control = this.TxtFilter;
            this.c1CommandControl1.Name = "c1CommandControl1";
            this.c1CommandControl1.ShortcutText = "";
            // 
            // TxtFilter
            // 
            this.TxtFilter.Captain = "过滤框";
            this.TxtFilter.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFilter.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFilter.CaptainWidth = 55F;
            this.TxtFilter.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFilter.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFilter.EditMask = null;
            this.TxtFilter.Location = new System.Drawing.Point(523, 10);
            this.TxtFilter.Multiline = false;
            this.TxtFilter.Name = "TxtFilter";
            this.TxtFilter.PasswordChar = '\0';
            this.TxtFilter.ReadOnly = false;
            this.TxtFilter.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFilter.SelectionStart = 0;
            this.TxtFilter.SelectStart = 0;
            this.TxtFilter.Size = new System.Drawing.Size(345, 23);
            this.TxtFilter.TabIndex = 5;
            this.TxtFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFilter.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFilter.Watermark = "输入供货商名称、经手人姓名进行过滤";
            this.TxtFilter.TextChanged += new System.EventHandler(this.TxtFilter_TextChanged);
            // 
            // c1CommandControl2
            // 
            this.c1CommandControl2.Name = "c1CommandControl2";
            this.c1CommandControl2.ShortcutText = "";
            // 
            // c1CommandLink3
            // 
            this.c1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink3.Command = this.CmdQuery;
            this.c1CommandLink3.SortOrder = 3;
            // 
            // c1CommandLink5
            // 
            this.c1CommandLink5.Command = this.c1CommandControl3;
            this.c1CommandLink5.SortOrder = 2;
            // 
            // c1CommandLink1
            // 
            this.c1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink1.Command = this.Cmd_Del;
            this.c1CommandLink1.SortOrder = 1;
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink4.Command = this.Cmd_Add;
            // 
            // c1CommandLink9
            // 
            this.c1CommandLink9.Command = this.c1CommandControl1;
            this.c1CommandLink9.Delimiter = true;
            this.c1CommandLink9.SortOrder = 5;
            // 
            // c1ToolBar1
            // 
            this.c1ToolBar1.AccessibleName = "Tool Bar";
            this.c1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.c1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.c1ToolBar1.CommandHolder = null;
            this.c1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink4,
            this.c1CommandLink1,
            this.c1CommandLink5,
            this.c1CommandLink3,
            this.c1CommandLink10,
            this.c1CommandLink9});
            this.c1ToolBar1.Controls.Add(this.doubleDateEdit1);
            this.c1ToolBar1.Controls.Add(this.TxtFilter);
            this.c1ToolBar1.Controls.Add(this.LblTotal);
            this.c1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.c1ToolBar1.Name = "c1ToolBar1";
            this.c1ToolBar1.Size = new System.Drawing.Size(869, 44);
            this.c1ToolBar1.Text = "c1ToolBar1";
            this.c1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.c1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.c1ToolBar1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.myGrid1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(992, 450);
            this.tableLayoutPanel1.TabIndex = 1;
            // 
            // Yf_Rk1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(992, 450);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "Yf_Rk1";
            this.Text = "药房入库";
            this.Load += new System.EventHandler(this.Yf_Rk1_Load);
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.c1ToolBar1.ResumeLayout(false);
            this.c1ToolBar1.PerformLayout();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private CustomControl.MyGrid myGrid1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink10;
        private C1.Win.C1Command.C1CommandControl Control_Lbl;
        private System.Windows.Forms.Label LblTotal;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1Command CmdQuery;
        private C1.Win.C1Command.C1CommandControl c1CommandControl3;
        private CustomControl.DoubleDateEdit doubleDateEdit1;
        private C1.Win.C1Command.C1Command Cmd_Del;
        private C1.Win.C1Command.C1Command Cmd_Add;
        private C1.Win.C1Command.C1CommandControl c1CommandControl1;
        private CustomControl.MyTextBox TxtFilter;
        private C1.Win.C1Command.C1CommandControl c1CommandControl2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private C1.Win.C1Command.C1ToolBar c1ToolBar1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1CommandLink c1CommandLink1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink5;
        private C1.Win.C1Command.C1CommandLink c1CommandLink3;
        private C1.Win.C1Command.C1CommandLink c1CommandLink9;
    }
}