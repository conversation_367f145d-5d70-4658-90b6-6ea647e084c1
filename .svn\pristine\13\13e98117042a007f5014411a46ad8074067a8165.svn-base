﻿using Common.BaseForm;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using C1.Win.C1Command;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using BLL;
using YdPara;
using YdPublicFunction;

namespace 药店管理系统
{
    public partial class MainForm : Base
    {
        public MainForm()
        {
            InitializeComponent();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            Common.WinFormVar.Var.MainTab = this.MainTab;
            // 动态生成主菜单
            GenerateMenu();
            GenerateToolbar();
            ShowMessage();
        }

        #region 动态生成按钮
        private void GenerateMenu()
        {
            // 清空现有的菜单项
            menuStrip1.Items.Clear();

            foreach (var mdlSysMenu1 in YdVar.Var.UserPermission)
            {
                // 创建一级菜单命令
                ToolStripMenuItem firstLevelCommand = new ToolStripMenuItem();
                firstLevelCommand.Name = mdlSysMenu1.FrsCode;
                firstLevelCommand.Text = mdlSysMenu1.FrsName;
                firstLevelCommand.Image = YdResources.C_Resources.GetImage16(mdlSysMenu1.FrsName);
                firstLevelCommand.TextImageRelation = TextImageRelation.ImageBeforeText;
                menuStrip1.Items.Add(firstLevelCommand);


                foreach (var mdlSysMenu2 in mdlSysMenu1.Menu2Lists)
                {
                    // 创建二级菜单命令
                    // ToolStripMenuItem secondLevelCommand = new ToolStripMenuItem();
                    // secondLevelCommand.Name = mdlSysMenu2.ScndCode;
                    // secondLevelCommand.Text = mdlSysMenu2.ScndName;
                    // secondLevelCommand.Image = YdResources.C_Resources.GetImage16(mdlSysMenu2.ScndName);
                    // secondLevelCommand.TextImageRelation = TextImageRelation.ImageBeforeText;

                    foreach (var mdlSysModule in mdlSysMenu2.ModuleLists)
                    {
                        // 创建三级菜单命令（模块）
                        ToolStripMenuItem moduleCommand = new ToolStripMenuItem();
                        moduleCommand.Name = mdlSysModule.ModuleCode;
                        moduleCommand.Text = mdlSysModule.ModuleName;
                        moduleCommand.Image = YdResources.C_Resources.GetImage20(mdlSysModule.ModuleName);
                        moduleCommand.TextImageRelation = TextImageRelation.ImageBeforeText;
                        // 添加点击事件
                        moduleCommand.Click += (sender, e) =>
                        {
                            ToolStripMenuItem cmd = sender as ToolStripMenuItem;
                            if (cmd != null)
                            {
                                CallForm(moduleCommand.Text);
                            }
                        };
                        // 将模块链接添加到二级菜单
                        // secondLevelCommand.DropDownItems.Add(moduleCommand);
                        firstLevelCommand.DropDownItems.Add(moduleCommand);
                    }

                    // 将二级菜单链接添加到一级菜单
                    // firstLevelCommand.DropDownItems.Add(secondLevelCommand);
                }

                // 将一级菜单链接添加到主菜单
                menuStrip1.Items.Add(firstLevelCommand);
            }
        }
        private void GenerateToolbar()
        {
            foreach (DataRow row in Common.WinFormVar.Var.ConfigDbHelper.Query("Select * from OftenMoudle  order by MoudleTimes desc LIMIT 15").Tables[0].Rows)
            {
                if (YdPublicFunction.Permission.CheckModule(row["MoudleName"].ToString()))
                {
                    ToolStripButton tsb = new ToolStripButton();
                    tsb.Image = YdResources.C_Resources.GetImage16(row["MoudleName"].ToString());
                    tsb.Text = row["MoudleName"].ToString();
                    tsb.TextImageRelation = TextImageRelation.ImageAboveText;
                    tsb.Click += (sender, e) =>
                    {
                        ToolStripMenuItem cmd = sender as ToolStripMenuItem;
                        if (cmd != null)
                        {
                            CallForm(tsb.Text);
                        }
                    };
                    toolStrip1.Items.Insert(0, tsb);

                }
            }
            ToolBarExit.Image = YdResources.C_Resources.GetImage16(ToolBarExit.Text);
            ToolBarEditPwd.Image = YdResources.C_Resources.GetImage16(ToolBarEditPwd.Text);
        }
        #endregion

        #region 提醒
        private void ShowMessage()
        {
            // 检查临期预警天数配置
            BllZd_Yp3 bllZd_Yp3 = new BllZd_Yp3();
            if (bllZd_Yp3.GetRecordCount($"DATEDIFF(day, GETDATE(), Yp_ScDate2) <= {PublicConfig.ExpiryWarningDays} AND DATEDIFF(day, GETDATE(), Yp_ScDate2) >= 0") > 0)
            {
                YdQuery.Lq_Yj vform = new YdQuery.Lq_Yj();
                base.AddTabControl(vform, "临期预警", YdResources.C_Resources.GetImage16("临期预警"));

            }

        }
        #endregion

        #region 选项卡动作

        private void MainTab_TabPageClosing(object sender, TabPageCancelEventArgs e)
        {
            foreach (System.Windows.Forms.Form frm in Application.OpenForms)
            {
                foreach (object _ctrl in e.TabPage.Controls)
                {
                    if (_ctrl == frm)
                    {
                        frm.Close();
                        if (frm != null) e.Cancel = true;
                        return;
                    }
                }
            }
        }
        private void MainTab_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                MainTab.Close(MainTab.SelectedTab);
            }
        }

        private void MainTab_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                MainTab.ContextMenuStrip = ContextMenuStrip1;
            }
        }

        private void MainTab_MouseLeave(object sender, System.EventArgs e)
        {
            MainTab.ContextMenuStrip = null;
        }

        private void ToolStripMenuItem1_Click(object sender, System.EventArgs e)
        {
            MainTab.Close(MainTab.SelectedTab);
        }

        private void ToolStripMenuItem2_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            foreach (C1DockingTabPage tap in MainTab.TabPages)
            {
                if (!object.ReferenceEquals(tap, MainTab.SelectedTab))
                {
                    taparray.Add(tap);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem3_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            for (int pageindex = 0; pageindex <= MainTab.TabPages.Count - 1; pageindex++)
            {
                if (pageindex < MainTab.SelectedIndex)
                {
                    taparray.Add(MainTab.TabPages[pageindex]);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem4_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            for (int pageindex = 0; pageindex <= MainTab.TabPages.Count - 1; pageindex++)
            {
                if (pageindex > MainTab.SelectedIndex)
                {
                    taparray.Add(MainTab.TabPages[pageindex]);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem5_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            foreach (C1DockingTabPage tap in MainTab.TabPages)
            {
                taparray.Add(tap);
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        #endregion

        #region 事件
        private void CallForm(string ModuleName)
        {
            Common.BaseForm.Base frm;
            switch (ModuleName)
            {
                case "角色管理":
                    frm = new YdSysManage.DictRole1();
                    break;
                case "用户管理":
                    frm = new YdSysManage.SysUserManage1();
                    break;
                case "药品大类":
                    frm = new YdBaseDict.Zd_Yp1_1();
                    break;
                case "药品字典":
                    frm = new YdBaseDict.Zd_Yp_Dict1();
                    break;
                case "特殊药品":
                    frm = new YdBaseDict.Zd_Yp2_Ts1();
                    break;
                case "药品剂型":
                    frm = new YdBaseDict.Zd_Jx1();
                    break;
                case "货架信息":
                    frm = new YdBaseDict.Zd_Hj1();
                    break;
                case "存储条件":
                    frm = new YdBaseDict.Zd_Cctj1();
                    break;
                case "医疗器械":
                    frm = new YdBaseDict.Zd_Ylqx1();
                    break;
                case "支付方式":
                    frm = new YdBaseDict.Zd_Pay1();
                    break;
                case "会员管理":
                    frm = new YdBaseDict.Zd_Hy1();
                    break;
                case "供应商":
                    frm = new YdBaseDict.Zd_Kh1();
                    break;
                case "处方单位":
                    frm = new YdBaseDict.Zd_KjDw1();
                    break;
                case "设备检查记录":
                    frm = new YdGSP.Zd_SbJc1();
                    break;
                case "人员培训计划及记录":
                    frm = new YdGSP.Zd_PxJh1();
                    break;
                case "人员体检计划及记录":
                    frm = new YdGSP.Zd_TjJh1();
                    break;
                case "不良反应记录":
                    frm = new YdGSP.Zd_Blfy1();
                    break;
                case "药店信息":
                    frm = new YdBaseDict.Zd_Yd1();
                    break;
                case "采购订单":
                    frm = new YdBusiness.Dd_Order1();
                    break;
                case "药品调价":
                    frm = new YdBusiness.Yp_Tj();
                    break;
                case "收银员交班":
                    frm = new YdBusiness.Jb_Sy();
                    break;
                case "收银员交班记录":
                    frm = new YdQuery.Jb_Cx();
                    break;
                case "销售查询":
                    frm = new YdQuery.XsCx();
                    break;
                case "采购验收":
                    frm = new YdBusiness.Ys_Order1();
                    break;
                case "不合格药品锁定":
                    frm = new YdGSP.Yp_Bhg1();
                    break;
                case "系统参数":
                    frm = new YdSysManage.Sysconfig();
                    break;
                case "销售记录":
                    frm = new YdBusiness.XsCk1();
                    break;
                case "销售出库":
                    BLL.BllYk_Ck1 _bllYkCk1 = new BllYk_Ck1();
                    DataTable dt = _bllYkCk1.GetList("1=2").Tables[0];
                    DataRow MyRow = dt.NewRow();
                    frm = new YdBusiness.XsCk2(true, MyRow, dt);
                    frm.Tag = MyRow["Ck_Code"];
                    break;
                default:
                    return;
            }
            UpdatBtnCallTimes(ModuleName);
            base.AddTabControl(frm, ModuleName, YdResources.C_Resources.GetImage16(ModuleName));
        }
        private void UpdatBtnCallTimes(string ModuleName)
        {
            if (int.Parse(Common.WinFormVar.Var.ConfigDbHelper.GetSingle("Select Count(MoudleName) from OftenMoudle where MoudleName='" + ModuleName + "'").ToString()) > 0)
            {
                Common.WinFormVar.Var.ConfigDbHelper.ExecuteSql("Update OftenMoudle set MoudleTimes=MoudleTimes+1 where MoudleName='" + ModuleName + "'");
            }
            else
            {
                Common.WinFormVar.Var.ConfigDbHelper.ExecuteSql("insert into OftenMoudle values('" + ModuleName + "',1)");
            }
        }
        #endregion

        private void ToolBarExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void ToolBarEditPwd_Click(object sender, EventArgs e)
        {
            // 检查是否已登录
            if (string.IsNullOrEmpty(YdVar.Var.JsrCode))
            {
                MessageBox.Show("请先登录系统！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 打开修改密码窗体
            YdSysManage.EditPassword editPasswordForm = new YdSysManage.EditPassword();
            DialogResult result = editPasswordForm.ShowDialog(this);

            if (result == DialogResult.OK)
            {
                MessageBox.Show("密码修改成功，请重新登录！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
