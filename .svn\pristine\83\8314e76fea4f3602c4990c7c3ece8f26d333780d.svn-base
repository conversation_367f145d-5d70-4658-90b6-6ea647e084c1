﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Common;
using CustomControl;
using Newtonsoft.Json;
using YdPublicFunction.Mdl;

namespace YdPublicFunction
{
    public class CentralApi
    {
        /// <summary>
        /// 根据电话号码获取数据库连接信息
        /// </summary>
        /// <param name="mdlGetDbConfigByTeleIn">输入参数</param>
        /// <param name="msg">返回消息</param>
        /// <param name="mdlOutPara">输出参数</param>
        /// <returns>状态码</returns>
        public static int GetDbConfigByTele(MdlGetDbConfigByTeleIn mdlGetDbConfigByTeleIn, ref string msg, ref MdlGetDbConfigByTeleOut mdlOutPara)
        {
            int result;
            string action = "/api/Zd_Yd/GetDbConfigByTele";

            result = SendMsg<MdlGetDbConfigByTeleIn, MdlGetDbConfigByTeleOut>(action, HttpVerb.POST, mdlGetDbConfigByTeleIn, ref msg, ref mdlOutPara, "data");
            return result;
        }
        public static int SendMsg<T, Out>(string action, HttpVerb method, T mdlInput, ref string msg, ref Out mdlOutput, string dataLb)
        {
            string timestamp;
            string url = "https://103.89.220.138:7747/api";
            MdlOutPara<Out> mdlOutputpara = new MdlOutPara<Out>();

            RestClient restClient = new RestClient(url + action);
            restClient.ContentType = "Application/json";
            restClient.Method = method;
            restClient.Timeout = 1000 * 60 * 5; //超时改成5分钟
            timestamp = TimeHelp.GetTimeStamp(DateTime.Now, 10);
            restClient.PostData = JsonConvert.SerializeObject(mdlInput);

            string outAll = restClient.MakeRequest();

            if (restClient.StatusCode != HttpStatusCode.OK)
            {
                msg = restClient.MakeRequest();
                return -2;
            }
            else
            {
                mdlOutputpara = JsonConvert.DeserializeObject<MdlOutPara<Out>>(outAll);
                switch (dataLb)
                {

                    case "list":
                        mdlOutput = mdlOutputpara.list;
                        break;
                    case "data":
                        mdlOutput = mdlOutputpara.data;
                        break;
                    default:
                        break;
                }

                msg = mdlOutputpara.stateMsg;
                return Convert.ToInt32(mdlOutputpara.stateCode);

            }


        }
    }
}
