using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace YdControl
{
    public partial class ComboCfdw : MyDtComobo
    {
        public ComboCfdw()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            BLL.BllZd_KjDw _bllZd_KjDw = new BllZd_KjDw();
            this.DataView = _bllZd_KjDw.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("CfDw_Name", "处方单位名称", 150, "左");
            // this.Init_Colum("CfDw_Code", "编码", 60, "左");
            // this.Init_Colum("CfDw_Jc", "简称", 80, "左");
            // this.Init_Colum("CfDw_Memo", "备注", 120, "左");
            this.DisplayMember = "CfDw_Name";
            this.ValueMember = "CfDw_Code";
            int width = 350;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "CfDw_Code+isnull(CfDw_Jc,'')+CfDw_Name";
        }
    }
}
