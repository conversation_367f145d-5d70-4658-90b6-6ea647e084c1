﻿/**  版本信息模板在安装目录下，可自行修改。
* BllYk_Rk2.cs
*
* 功 能： N/A
* 类 名： BllYk_Rk2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/8/1 9:27:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using Model;
using DALFactory;
using IDAL;
namespace BLL
{
    /// <summary>
    /// BllYk_Rk2
    /// </summary>
    public partial class BllYk_Rk2
    {
        private readonly IDalYk_Rk2 dal = DataAccess.CreateDalYk_Rk2();
        public BllYk_Rk2()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return dal.GetMaxId();
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int Rk_Id)
        {
            return dal.Exists(Rk_Id);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public int Add(Model.MdlYk_Rk2 model, ref string Rk_Id)
        {
            return dal.Add(model, ref Rk_Id);
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.MdlYk_Rk2 model)
        {
            return dal.Update(model);
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int Rk_Id)
        {

            return dal.Delete(Rk_Id);
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool DeleteList(string Rk_Idlist)
        {
            return dal.DeleteList(Rk_Idlist);
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlYk_Rk2 GetModel(int Rk_Id)
        {

            return dal.GetModel(Rk_Id);
        }

        /// <summary>
        /// 得到一个对象实体，从缓存中
        /// </summary>
        public Model.MdlYk_Rk2 GetModelByCache(int Rk_Id)
        {

            string CacheKey = "MdlYk_Rk2Model-" + Rk_Id;
            object objModel = Common.DataCache.GetCache(CacheKey);
            if (objModel == null)
            {
                try
                {
                    objModel = dal.GetModel(Rk_Id);
                    if (objModel != null)
                    {
                        int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
                        Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
                    }
                }
                catch { }
            }
            return (Model.MdlYk_Rk2)objModel;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            return dal.GetList(strWhere);
        }
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            return dal.GetList(Top, strWhere, filedOrder);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public List<Model.MdlYk_Rk2> GetModelList(string strWhere)
        {
            DataSet ds = dal.GetList(strWhere);
            return DataTableToList(ds.Tables[0]);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public List<Model.MdlYk_Rk2> DataTableToList(DataTable dt)
        {
            List<Model.MdlYk_Rk2> modelList = new List<Model.MdlYk_Rk2>();
            int rowsCount = dt.Rows.Count;
            if (rowsCount > 0)
            {
                Model.MdlYk_Rk2 model;
                for (int n = 0; n < rowsCount; n++)
                {
                    model = dal.DataRowToModel(dt.Rows[n]);
                    if (model != null)
                    {
                        modelList.Add(model);
                    }
                }
            }
            return modelList;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetAllList()
        {
            return GetList("");
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            return dal.GetRecordCount(strWhere);
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            return dal.GetListByPage(strWhere, orderby, startIndex, endIndex);
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        //public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        //{
        //return dal.GetList(PageSize,PageIndex,strWhere);
        //}

        #endregion  BasicMethod

        #region  ExtensionMethod

        public DataTable GetListByRkCode(string Rk_Code)
        {
            return dal.GetListByRkCode(Rk_Code);
        }

        public bool UpdateYpCode(int RkId, string YpCode)
        {
            return dal.UpdateYpCode(RkId, YpCode);
        }

        public DataTable GetFirstYpDjList(string XlCode)
        {
            return dal.GetFirstYpDjList(XlCode);
        }

        #endregion  ExtensionMethod
    }
}

