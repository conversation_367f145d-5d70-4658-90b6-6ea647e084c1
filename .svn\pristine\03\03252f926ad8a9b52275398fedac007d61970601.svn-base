using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using YdPublicFunction;
using YdPublicFunction.Mdl;
using Common;
using C1.Win.C1TrueDBGrid;

namespace YdSysManage
{
    public partial class MessageList : Common.BaseForm.BaseFather
    {
        bool _isInitOver = false;
        List<MdlGetMessageReadListOut> outputList = null;
        private CurrencyManager MyCm;

        public MessageList()
        {
            InitializeComponent();
        }

        private void MessageList_Load(object sender, EventArgs e)
        {
            FormInit();
        }

        private void FormInit()
        {
            _isInitOver = false;
            // 初始化Grid
            myGrid1.Init_Grid();
            myGrid1.Init_Column("消息标题", "Title", 250, "左", "", false);
            myGrid1.Init_Column("消息内容", "MessageContent", 1000, "左", "", false);
            myGrid1.Init_Column("发送者", "SenderName", 100, "中", "", false);
            myGrid1.Init_Column("读取状态", "ReadStatus", 100, "中", "", false);
            myGrid1.Init_Column("发布时间", "PublishDate", 150, "中", "", false);
            myGrid1.AllowUpdate = false;

            // 初始化ComboBox
            scbMessgeStatus.Additem = "全部";
            scbMessgeStatus.Additem = "未读";
            scbMessgeStatus.Additem = "已读";
            scbMessgeStatus.DisplayColumns[1].Visible = false;
            scbMessgeStatus.DroupDownWidth = scbMessgeStatus.Width - (int)scbMessgeStatus.CaptainWidth;
            scbMessgeStatus.ItemHeight = 20;
            _isInitOver = true;
            scbMessgeStatus.SelectedIndex = 1;
        }

        private void LoadMessageList()
        {
            try
            {
                // 准备输入参数
                MdlGetMessageReadListIn inputModel = new MdlGetMessageReadListIn();
                inputModel.Yd_Code = YdVar.Var.Yd_Code; // 获取当前药店代码
                if (scbMessgeStatus.Text != "全部")
                {
                    inputModel.ReadStatus = scbMessgeStatus.Text;
                }

                // 调用接口
                string msg = "";
                int result = CentralApi.GetMessageReadList(inputModel, ref msg, ref outputList);

                if (result == 0 && outputList != null)
                {
                    var dt = outputList.OrderByDescending(p => p.PublishDate).ToList();
                    MyCm = (CurrencyManager)BindingContext[dt, ""];
                    myGrid1.DataTable = dt;
                    // 更新总数标签
                    lblTotal.Text = $"共 {outputList.Count} 条消息";
                }
                else
                {
                    MessageBox.Show($"加载消息列表失败：{msg}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载消息列表异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MarkMessageAsRead(int ReadID)
        {
            try
            {
                MdlMessageReadIn inputModel = new MdlMessageReadIn();
                inputModel.ReadID = ReadID;
                inputModel.Jsr_Code = YdVar.Var.Yd_Code;
                inputModel.Jsr_Name = YdVar.Var.Yd_Name;

                string msg = "";
                MdlMessageReadOut outputModel = null;

                int result = CentralApi.MessageRead(inputModel, ref msg, ref outputModel);

                if (result == 0)
                {
                    // 标记成功，刷新列表
                    LoadMessageList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"标记消息已读失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void scbMessgeStatus_SelectedValueChanged(object sender, EventArgs e)
        {
            if (!_isInitOver) return;
            LoadMessageList();
        }

        private void myGrid1_DoubleClick(object sender, EventArgs e)
        {
            // 双击消息时标记为已读
            if (myGrid1.DataSource != null && myGrid1.Row >= 0)
            {
                MdlGetMessageReadListOut messageData = (MdlGetMessageReadListOut)MyCm.List[myGrid1.Row];
                MessageDetail vform = new MessageDetail(messageData);
                vform.Tag = messageData.MessageId;
                // 如果是未读消息，标记为已读
                if (messageData.ReadStatus == "未读")
                {
                    MarkMessageAsRead(messageData.ReadID);
                }
                base.AddTabControl(vform, "消息详情-" + (messageData.Title), YdResources.C_Resources.GetImage16(""));
            }
        }
    }
}