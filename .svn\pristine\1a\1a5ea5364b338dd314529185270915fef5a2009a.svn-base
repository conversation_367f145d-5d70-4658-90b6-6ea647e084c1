﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlZd_Yp3.cs
*
* 功 能： N/A
* 类 名： MdlZd_Yp3
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlZd_Yp3:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlZd_Yp3
	{
		public MdlZd_Yp3()
		{}
		#region Model
		private string _xl_code;
		private string _yp_code;
		private string _yp_scph;
		private DateTime? _yp_scdate1;
		private DateTime? _yp_scdate2;
		private string _yp_memo;
		private decimal? _yp_count;
		private bool _sc_finish;
		private int? _rk_sl;
		/// <summary>
		/// 
		/// </summary>
		public string Xl_Code
		{
			set{ _xl_code=value;}
			get{return _xl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Code
		{
			set{ _yp_code=value;}
			get{return _yp_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Scph
		{
			set{ _yp_scph=value;}
			get{return _yp_scph;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Yp_ScDate1
		{
			set{ _yp_scdate1=value;}
			get{return _yp_scdate1;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Yp_ScDate2
		{
			set{ _yp_scdate2=value;}
			get{return _yp_scdate2;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Memo
		{
			set{ _yp_memo=value;}
			get{return _yp_memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Yp_Count
		{
			set{ _yp_count=value;}
			get{return _yp_count;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Sc_Finish
		{
			set{ _sc_finish=value;}
			get{return _sc_finish;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Rk_Sl
		{
			set{ _rk_sl=value;}
			get{return _rk_sl;}
		}
		#endregion Model

	}
}

