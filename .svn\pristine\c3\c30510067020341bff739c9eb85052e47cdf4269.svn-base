﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Common.Delegate;
using Model;
using YdPublicFunction;
using Common;
using YdVar;
using System.Transactions;
using C1.Win.C1TrueDBGrid;
using Stimulsoft.Report;

namespace YdBusiness
{
    public partial class XsCk2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BLL.BllYk_Ck1 _bllYkCk1 = new BllYk_Ck1();
        private BLL.BllYk_Ck2 _bllYkCk2 = new BllYk_Ck2();
        private Model.MdlYk_Ck1 _mdlYkCk1 = new MdlYk_Ck1();
        private decimal Ck_Money = 0;
        bool subInsert;
        private DataTable tracTable;
        private BllYk_CkDrugtracinfo _bllYkCkDrugtracinfo = new BllYk_CkDrugtracinfo();
        public XsCk2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = insert;
            ZbTable = table;
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void XsCk2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert)
                Zb_Clear();
            else
                Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
        }
        private void XsCk2_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {

            BtnDeleteAll.Location = new Point(30, 1);
            BtnNew.Location = new Point(BtnDeleteAll.Left + BtnDeleteAll.Width + 2, 1);
            BtnSave.Location = new Point(BtnNew.Left + BtnNew.Width + 2, 1);
            BtnSettle.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);
            BtnPrint.Location = new Point(BtnSettle.Left + BtnSettle.Width + 2, 1);
            BtnClose.Location = new Point(BtnPrint.Left + BtnPrint.Width + 2, 1);

            TxtCode.Enabled = false;
            NumCkMoney.Enabled = false;
            NumYpCk_Money.Enabled = false;

            DtpDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.EditFormat = "yyyy-MM-dd HH:mm:ss";

            // 初始化药品批号下拉菜单
            comboYpPh1.Init();

            // 初始化支付方式下拉菜单
            comboPay1.Init();

            myGrid1.Init_Grid();
            // myGrid1.Init_Column("明细ID", "Ck_ID", 80, "中", "", false);
            myGrid1.Init_Column("药品编码", "Yp_Code", 100, "中", "", false);
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 150, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("包装规格", "Yp_Bzgg", 120, "左", "", false);
            myGrid1.Init_Column("制剂单位", "Yp_Zjdw", 100, "中", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 60, "中", "", false);
            myGrid1.Init_Column("数量", "Ck_Sl", 70, "右", "#,##0.00", false);
            myGrid1.Init_Column("追溯码", "traccnt", 70, "右", "###,###,##0.####", false);
            myGrid1.Init_Column("单价", "Ck_Dj", 70, "右", "#,##0.00", false);
            myGrid1.Init_Column("金额", "Ck_Money", 70, "右", "#,##0.00", false);
            myGrid1.Init_Column("备注", "Ck_Memo", 200, "左", "", false);
            myGrid1.Splits[0].DisplayColumns["traccnt"].FetchStyle = true;
            myGrid1.AllowSort = true;
            myGrid1.AllowAddNew = true;

            myGrid2.Init_Grid();
            myGrid2.Init_Column("追溯码", "drug_trac_codg", 100, "中", "", false);

            BaseMyGrid = myGrid1;
        }
        private void BtnState()
        {
            if (_mdlYkCk1.Ck_Ok.IsNullOrEmpty() || _mdlYkCk1.Ck_Ok == "未结算")
            {
                BtnDeleteAll.Enabled = true;
                BtnNew.Enabled = true;
                BtnSave.Enabled = true;
                BtnClose.Enabled = true;
                BtnSettle.Enabled = true;
                BtnPrint.Enabled = true;
                BtnSettleCancel.Enabled = false;
                ControlEnable(true);
            }
            else
            {
                BtnDeleteAll.Enabled = false;
                BtnNew.Enabled = false;
                BtnSave.Enabled = false;
                BtnClose.Enabled = true;
                BtnSettle.Enabled = false;
                BtnPrint.Enabled = true;
                BtnSettleCancel.Enabled = true;
                ControlEnable(false);
            }

        }
        private void ControlEnable(bool flag)
        {
            TxtGkName.Enabled = flag;
            TxtGkTele.Enabled = flag;
            TxtGkSfzh.Enabled = flag;
            NumHyZk.Enabled = flag;
            NumJsMoney.Enabled = flag;
            comboPay1.Enabled = flag;
            DtpDate.Enabled = flag;
            TxtMemo.Enabled = flag;
            comboYpPh1.Enabled = flag;
            TxtTraceCode.Enabled = flag;
            NumCk_Dj.Enabled = flag;
            NumCk_Sl.Enabled = flag;
            toolStrip2.Enabled = flag;
            myGrid1.AllowAddNew = flag;
        }
        #endregion

        #region  显示函数
        protected override void Zb_Clear()
        {
            base.Insert = true;
            _mdlYkCk1 = new MdlYk_Ck1();
            ZbRow = ZbTable.NewRow();
            TxtCode.Text = _bllYkCk1.MaxCode(9);
            TxtGkName.Text = "";
            TxtGkTele.Text = "";
            TxtGkSfzh.Text = "";
            NumHyZk.Value = 1; // 默认折扣为1（无折扣）
            NumJsMoney.Value = 0;
            comboPay1.SelectedIndex = -1;
            DtpDate.Value = DateTime.Now;
            NumCkMoney.Value = 0;
            TxtMemo.Text = "";
            comboYpPh1.SelectedIndex = -1;
            DataClear();
            ZbPictureShow("");
            TxtTraceCode.Select();
        }

        private void Zb_Show()
        {
            _mdlYkCk1 = _bllYkCk1.GetModel(ZbRow["Ck_Code"] + "");
            TxtCode.Text = _mdlYkCk1.Ck_Code;
            TxtGkName.Text = _mdlYkCk1.Gk_Name;
            TxtGkTele.Text = _mdlYkCk1.Gk_Tele;
            TxtGkSfzh.Text = _mdlYkCk1.Gk_Sfzh;
            NumHyZk.Value = _mdlYkCk1.Hy_Zk ?? 1;
            NumJsMoney.Value = _mdlYkCk1.Js_Money ?? 0;
            comboPay1.Text = _mdlYkCk1.Pay_Type;
            DtpDate.Value = _mdlYkCk1.Ck_Date;
            NumCkMoney.Value = _mdlYkCk1.Ck_Money ?? 0;
            TxtMemo.Text = _mdlYkCk1.Ck_Memo;
            DataClear();
            ZbPictureShow(_mdlYkCk1.Ck_Ok);
            TxtGkName.Select();
        }
        private void ZbPictureShow(string Ck_Ok)
        {
            if (Ck_Ok == "已结算")
            {
                pictureBox1.Image = YdResources.StateRes.已完成;
            }
            if (Ck_Ok == "未结算")
            {
                pictureBox1.Image = YdResources.StateRes.未完成;
            }
            if (Ck_Ok == "未结算")
            {
                pictureBox1.Image = YdResources.StateRes.新单;
            }

        }
        private void Cb_Show()
        {
            MyTable = _bllYkCk2.GetList($"Ck_Code='{_mdlYkCk1.Ck_Code}'").Tables[0];
            MyTable.TableName = "明细";
            MyTable.Columns["Ck_Id"].ReadOnly = false;
            MyTable.Columns["traccnt"].ReadOnly = false;
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            DataSum("");

            tracTable = _bllYkCkDrugtracinfo.GetList($"Ck_Code='{_mdlYkCk1.Ck_Code}'").Tables[0];
            tracTable.TableName = "追溯码";
            tracTable.Columns["drug_trac_codg"].ReadOnly = false;
            myGrid2.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid2.DataTable = p;
            }), tracTable);
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllYkCk1.GetRecordCount("Ck_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此销售出库单已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(comboPay1)) return false;
            if (DtpDate.Value == null)
            {
                MessageBox.Show("请选择出库日期!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                DtpDate.Focus();
                return false;
            }
            if ((DateTime)DtpDate.Value > Convert.ToDateTime("2079-06-01"))
            {
                DtpDate.Select();
                MessageBox.Show("填写的出库日期超出范围，请重新输入!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }
        private bool ZbStateCheck(string state)
        {
            if (state == "从表修改")
            {
                // 从表修改时的检查逻辑
            }
            if (state == "删除")
            {
                if (!this.Insert && _bllYkCk1.GetRecordCount("Ck_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此销售出库单已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }

            if (state == "完成")
            {
                //检测追溯码
                int cnt = MyTable.AsEnumerable().Where(p => p.Field<int?>("traccnt") == null || Math.Abs(p.Field<decimal>("Ck_Sl")) != Convert.ToDecimal(p.Field<int>("traccnt"))).Count();
                if (cnt > 0)
                {
                    MessageBox.Show("没有扫全药品追溯码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数
        //删除行
        protected override bool DataDeleteOne()
        {
            if (myGrid1.Row >= myGrid1.RowCount)
            {
                MessageBox.Show("请选择一条记录!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            if (base.DataDeleteOne() == true)
            {
                _bllYkCk2.Delete(int.Parse(base.SubItemRow["Ck_Id"] + ""));
                MyTable.AcceptChanges();
                DataSum("");
                LblTotal.Text = "∑=" + (MyTable.Rows.Count).ToString();
                return true;
            }
            return false;
        }

        protected override bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {

            if (_mdlYkCk1 == null)
            {
                MessageBox.Show("数据尚未保存,无法删除!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            ZbRow.Delete();
            return base.DataDeleteAll(PrimaryKey, Delete);
        }

        protected override void DataNew()
        {
            myGrid1.UpdateData();
            if (base.MyTable.DataSet.HasChanges() == true)
            {
                if (MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.OK)
                {
                    DataSave(false);
                }
            }

            Zb_Clear();
            BtnState();
            Cb_Show();
            TxtGkName.Select();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            bool insertOriginal = base.Insert;
            try
            {
                using (TransactionScope scope = new TransactionScope())
                {
                    if (base.Insert == true) TxtCode.Text = _bllYkCk1.MaxCode(9);
                    DataSum("");
                    if (base.Insert == true)
                    {
                        //增加记录
                        Zb_Add();
                    }
                    else
                    {
                        //编辑记录
                        Zb_Edit();
                    }
                    CbDataSave();
                    TracDataSave();
                    // 提交事务
                    scope.Complete();

                    if (showMsgbox == true) MessageBox.Show("数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                base.Insert = insertOriginal;
                // 事务会自动回滚
                MessageBox.Show($"药品出库操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
        protected override void DataComplete()
        {
            if (ZbStateCheck("完成") == false) return;
            if (MessageBox.Show("是否结算此单据？", "提示:", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.No)
                return;
            if (DataSave(false) == false) return;
            Cb_Show();
            if (_bllYkCk1.Complete(_mdlYkCk1.Ck_Code))
            {
                _mdlYkCk1.Ck_Ok = "已结算";
                ZbRow["Ck_Ok"] = "已结算";
                ZbPictureShow("已结算");
                BtnState();
                if (MessageBox.Show("操作已完成，是否进行打印？", "提示:", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                {
                    DataPrint();
                }
            }
        }
        private void ZbSettleCancel()
        {
            if (ZbStateCheck("取消结算") == false) return;
            if (MessageBox.Show("是否取消结算此单据？", "提示:", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.No)
                return;
            if (_bllYkCk1.CancelSettle(_mdlYkCk1.Ck_Code))
            {
                _mdlYkCk1.Ck_Ok = "未结算";
                ZbRow["Ck_Ok"] = "未结算";
                ZbPictureShow("未结算");
                BtnState();
                MessageBox.Show("结算撤销已完成！", "提示:", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1);

            }
        }
        protected override void DataPrint()
        {
            if (_mdlYkCk1 == null)
            {
                MessageBox.Show("数据尚未保存,无法打印!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }

            if (_mdlYkCk1.Ck_Ok != "已结算")
            {
                MessageBox.Show("结算之后才能进行单据打印!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            StiReport Stirpt = new StiReport();
            Assembly assembly = Assembly.Load(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Namespace);
            var rpt = assembly.GetManifestResourceStream($"{System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Namespace}.Rpt.药店销售小票.mrt");
            Stirpt.Load(rpt);
            Stirpt.ReportName = "药店销售小票(" + string.Format("{0:yyMMdd}", System.DateTime.Now) + ")";
            Stirpt.RegData(base.MyTable);
            Stirpt.Compile();
            Stirpt["药店名称"] = YdVar.Var.Yd_Name;
            Stirpt["客户"] = TxtGkName.Text;
            Stirpt["操作员"] = YdVar.Var.UserName;
            Stirpt["单号"] = TxtCode.Text;
            Stirpt["合计金额"] = Ck_Money.ToString("0.00####");
            Stirpt["结算金额"] = _mdlYkCk1.Js_Money.Value.ToString("0.00####");
            Stirpt["日期"] = DateTime.Now.ToString("yyyy-MM-dd");
            Stirpt["时间"] = DateTime.Now.ToString("HH:mm:ss");
            Stirpt["地址"] = YdVar.Var.Yd_Address;

            // Stirpt.Design();
            Stirpt.Render();
            // Stirpt.Show();
            Stirpt.Print();
        }
        #endregion

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            Ck_Money = MyTable.Compute("Sum(Ck_Money)", "") == DBNull.Value ? 0 : Convert.ToDecimal(MyTable.Compute("Sum(Ck_Money)", ""));
            myGrid1.Columns["Ck_Money"].FooterText = string.Format("{0:####0.00##}", Ck_Money);
            NumCkMoney.BeginInvoke(new Action<decimal>(p =>
            {
                NumCkMoney.Value = p;
                // 当出库金额变化时，自动计算实际付款金额
                if (_frmInit && Convert.ToDecimal(NumHyZk.Value) > 0)
                {
                    NumJsMoney.Value = p * Convert.ToDecimal(NumHyZk.Value);
                }
            }), Ck_Money);
            LblTotal.BeginInvoke(new Action(() => { LblTotal.Text = "∑=" + base.MyTable.Rows.Count.ToString(); }));
            if (!_mdlYkCk1.Ck_Code.IsNullOrEmpty())
            {
                _mdlYkCk1.Ck_Money = Ck_Money;
                _mdlYkCk1.Js_Money = Ck_Money * (Convert.ToDecimal(NumHyZk.Value) > 0 ? Convert.ToDecimal(NumHyZk.Value) : 1);
                _bllYkCk1.Update(_mdlYkCk1);
                ZbRow["Ck_Money"] = Ck_Money;
                ZbRow["Js_Money"] = _mdlYkCk1.Js_Money;
            }
        }

        #region 主表

        //增加记录
        private void Zb_Add()
        {
            _mdlYkCk1.Ck_Code = _bllYkCk1.MaxCode(9);
            TxtCode.Text = _mdlYkCk1.Ck_Code;
            _mdlYkCk1.Gk_Name = TxtGkName.Text.Trim();
            _mdlYkCk1.Gk_Tele = TxtGkTele.Text.Trim();
            _mdlYkCk1.Gk_Sfzh = TxtGkSfzh.Text.Trim();
            _mdlYkCk1.Hy_Zk = (decimal?)NumHyZk.Value;
            _mdlYkCk1.Js_Money = (decimal?)NumJsMoney.Value;
            _mdlYkCk1.Pay_Type = comboPay1.Text + "";
            _mdlYkCk1.Ck_Date = (DateTime)DtpDate.Value;
            _mdlYkCk1.Ck_Money = (decimal?)NumCkMoney.Value;
            _mdlYkCk1.Ck_Memo = TxtMemo.Text.Trim();
            _mdlYkCk1.Czy_Code = YdVar.Var.JsrCode;
            _mdlYkCk1.Czy_Name = YdVar.Var.UserName;
            _mdlYkCk1.Yd_Code = YdVar.Var.Yd_Code;
            _mdlYkCk1.Ck_Ok = "未结算";
            _bllYkCk1.Add(_mdlYkCk1);
            Common.DataTableToList.ToDataRow<MdlYk_Ck1>(_mdlYkCk1, ZbRow);
            ZbTable.Rows.Add(ZbRow);
            base.Insert = false;
        }
        //编辑记录
        private void Zb_Edit()
        {
            _mdlYkCk1.Gk_Name = TxtGkName.Text.Trim();
            _mdlYkCk1.Gk_Tele = TxtGkTele.Text.Trim();
            _mdlYkCk1.Gk_Sfzh = TxtGkSfzh.Text.Trim();
            _mdlYkCk1.Hy_Zk = (decimal?)NumHyZk.Value;
            _mdlYkCk1.Js_Money = (decimal?)NumJsMoney.Value;
            _mdlYkCk1.Pay_Type = comboPay1.Text + "";
            _mdlYkCk1.Ck_Date = (DateTime)DtpDate.Value;
            _mdlYkCk1.Ck_Money = (decimal?)NumCkMoney.Value;
            _mdlYkCk1.Ck_Memo = TxtMemo.Text.Trim();
            _bllYkCk1.Update(_mdlYkCk1);
            Common.DataTableToList.ToDataRow<MdlYk_Ck1>(_mdlYkCk1, ZbRow);
        }
        #endregion

        #region 从表
        protected override void SubDataSum(int index)
        {
            DataRow upRow;
            //当 前 行
            upRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            switch (myGrid1.Splits[0].DisplayColumns[index].Name)
            {
                case "单价":
                case "数量":
                    if (upRow["Ck_Dj"] != DBNull.Value && upRow["Ck_Sl"] != DBNull.Value)
                    {
                        upRow["Ck_Money"] = Common.MathFormula.Multiply(upRow["Ck_Dj"].ToString(), upRow["Ck_Sl"].ToString());
                    }
                    break;
            }

            DataSum("");
        }
        protected override void SubDataEdit()
        {
            // if (ZbCheck() == false) return;
            // if (ZbStateCheck("从表修改") == false) return;
            // DataSave(false);
            // bool subInsert;
            // if ((myGrid1.Row + 1) > myGrid1.RowCount)
            // {
            //     base.SubItemRow = base.MyTable.NewRow();
            //     subInsert = true;
            // }
            // else
            // {
            //     base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            //     subInsert = false;
            // }

            // XsCk3 f = new XsCk3(_mdlYkCk1.Ck_Code, subInsert, base.SubItemRow, base.MyTable);
            // f.MyTransmitTxt = this.MyTransmitTxt;
            // f.Owner = this;
            // f.ShowDialog();

        }

        //从表更新
        private void CbDataSave()
        {
            myGrid1.UpdateData();
            foreach (DataRow _row in MyTable.Rows)
            {
                if (_row.RowState == DataRowState.Added)
                {
                    CbDataInsert(_row);
                }
                if (_row.RowState == DataRowState.Modified)
                {
                    CbDataUpdate(_row);
                }
                if (_row.RowState == DataRowState.Deleted)
                {
                    CbDataDelete(_row);
                }
            }
            MyTable.AcceptChanges();
        }

        //从表更新
        private void CbDataUpdate(DataRow Cb_Row)
        {
            Model.MdlYk_Ck2 mdlYk_Ck2 = _bllYkCk2.GetModel(int.Parse(Cb_Row["Ck_Id"].ToString()));
            mdlYk_Ck2 = Common.DataTableToList.ToModel(Cb_Row, mdlYk_Ck2);
            _bllYkCk2.Update(mdlYk_Ck2);
        }

        //从表增加
        private void CbDataInsert(DataRow Cb_Row)
        {
            Model.MdlYk_Ck2 mdlYk_Ck2 = new Model.MdlYk_Ck2();
            Cb_Row["Ck_Code"] = _mdlYkCk1.Ck_Code;
            mdlYk_Ck2 = Common.DataTableToList.ToModel<MdlYk_Ck2>(Cb_Row);
            int oldCk_Id = int.Parse(Cb_Row["Ck_Id"].ToString());
            Cb_Row["Ck_Id"] = _bllYkCk2.Add(mdlYk_Ck2);

            // 修改tracTable中的ck_id
            foreach (DataRow row in tracTable.AsEnumerable().Where(p => p.Field<int>("Ck_Id") == oldCk_Id))
            {
                row["Ck_Id"] = (int)Cb_Row["Ck_Id"];
            }
        }

        private void CbDataDelete(DataRow Cb_Row)
        {
            _bllYkCk2.Delete(int.Parse(Cb_Row["Ck_Id", DataRowVersion.Original].ToString()));
            DeleteTrac(int.Parse(Cb_Row["Ck_Id", DataRowVersion.Original].ToString()));
        }

        #endregion

        #region 追溯码数据操作

        //追溯码数据保存
        private void TracDataSave()
        {
            foreach (DataRow _row in tracTable.Rows)
            {
                if (_row.RowState == DataRowState.Added)
                {
                    TracDataInsert(_row);
                }
                if (_row.RowState == DataRowState.Modified)
                {
                    TracDataUpdate(_row);
                }
                if (_row.RowState == DataRowState.Deleted)
                {
                    TracDataDelete(_row);
                }
            }
            tracTable.AcceptChanges();
        }

        //追溯码数据更新
        private void TracDataUpdate(DataRow Trac_Row)
        {
            Model.MdlYk_CkDrugtracinfo mdlYkCkDrugtracinfo = _bllYkCkDrugtracinfo.GetModel(int.Parse(Trac_Row["Ck_Id"].ToString()), Trac_Row["drug_trac_codg"].ToString());
            mdlYkCkDrugtracinfo = Common.DataTableToList.ToModel(Trac_Row, mdlYkCkDrugtracinfo);
            _bllYkCkDrugtracinfo.Update(mdlYkCkDrugtracinfo);
        }

        //追溯码数据增加
        private void TracDataInsert(DataRow Trac_Row)
        {
            Model.MdlYk_CkDrugtracinfo mdlYkCkDrugtracinfo = new Model.MdlYk_CkDrugtracinfo();
            Trac_Row["Ck_Code"] = _mdlYkCk1.Ck_Code;
            mdlYkCkDrugtracinfo = Common.DataTableToList.ToModel<MdlYk_CkDrugtracinfo>(Trac_Row);
            _bllYkCkDrugtracinfo.Add(mdlYkCkDrugtracinfo);
        }

        //追溯码数据删除
        private void TracDataDelete(DataRow Trac_Row)
        {
            _bllYkCkDrugtracinfo.Delete(
                int.Parse(Trac_Row["Ck_Id", DataRowVersion.Original].ToString()),
                Trac_Row["drug_trac_codg", DataRowVersion.Original].ToString());
        }

        #endregion

        #region 追溯码

        private void DeleteTrac(int Ck_Id)
        {
            _bllYkCkDrugtracinfo.Delete(Ck_Id);
            // 收集要删除的行
            List<DataRow> rowsToDelete = new List<DataRow>();
            foreach (DataRow row in tracTable.AsEnumerable().Where(p => p.Field<int>("Ck_Id") == Ck_Id))
            {
                rowsToDelete.Add(row);
            }

            // 删除收集到的行
            foreach (DataRow row in rowsToDelete)
            {
                tracTable.Rows.Remove(row);
            }
            tracTable.AcceptChanges();
        }

        #endregion


        #endregion



        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DataDeleteOne();
        }
        private void BtnDeleteAll_Click(object sender, EventArgs e)
        {
            DataDeleteAll(_mdlYkCk1.Ck_Code, _bllYkCk1.Delete);
        }
        private void BtnNew_Click(object sender, EventArgs e)
        {
            DataNew();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            DataSave(true);
        }
        private void BtnSettle_Click(object sender, EventArgs e)
        {
            DataComplete();
        }
        private void BtnSettleCancel_Click(object sender, EventArgs e)
        {
            ZbSettleCancel();
        }
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            DataPrint();
        }
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        private void NumCk_Sl_Validated(object sender, EventArgs e)
        {
            if (NumCk_Dj.Value != DBNull.Value && NumCk_Sl.Value != DBNull.Value)
                NumYpCk_Money.Value = Math.Round((decimal)NumCk_Dj.Value * (decimal)NumCk_Sl.Value, 2);
        }
        private void NumCk_Sl_ValueChanged(object sender, EventArgs e)
        {
            if (NumCk_Dj.Value != DBNull.Value && NumCk_Sl.Value != DBNull.Value)
                NumYpCk_Money.Value = Math.Round((decimal)NumCk_Dj.Value * (decimal)NumCk_Sl.Value, 2);
        }


        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        private void myGrid1_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            string traccnt = grid.Columns["traccnt"].CellValue(e.Row).ToString();
            string Mz_Sl = grid.Columns["Ck_Sl"].CellValue(e.Row).ToString();
            switch (e.Column.DataColumn.DataField)
            {
                case "traccnt":
                    if (!string.IsNullOrEmpty(traccnt) && decimal.Parse(traccnt) == Math.Abs(decimal.Parse(Mz_Sl)))
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.完成16;
                    }
                    break;
            }
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.RightOfText;
        }
        private void myGrid1_RowColChange(object sender, RowColChangeEventArgs e)
        {
            if ((myGrid1.Row + 1) > myGrid1.RowCount || tracTable == null)
            {

            }
            else
            {
                DataRow MyRow = ((DataRowView)MyCm.List[myGrid1.Row]).Row;
                tracTable.DefaultView.RowFilter = $"Ck_Id={MyRow["Ck_Id"]}";
            }
        }
        #endregion

        #region 从表编辑

        private void DataClear()
        {
            subInsert = true;
            comboYpPh1.SelectedIndex = -1;
            NumCk_Sl.Value = 1;
            NumCk_Dj.Value = 0;
            NumYpCk_Money.Value = 0;
            comboYpPh1.Select();
        }
        private void DataShow(DataRow row)
        {
            subInsert = false;
            base.SubItemRow = row;
            comboYpPh1.SelectedValue = row["Yp_Code"] + "";
            NumCk_Sl.Value = decimal.Parse(row["Ck_Sl"] + "");
            NumCk_Dj.Value = decimal.Parse(row["Ck_Dj"] + "");
            NumYpCk_Money.Value = decimal.Parse(row["Ck_Money"] + "");
            comboYpPh1.Select();
        }
        private bool DataCheck()
        {
            if (comboYpPh1.SelectedValue == null)
            {
                MessageBox.Show("请选择药品！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                comboYpPh1.Select();
                return false;
            }
            if (NumCk_Sl.Value == DBNull.Value || NumCk_Sl.Value == null)
            {
                MessageBox.Show("数量不能为空！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCk_Sl.Select();
                return false;
            }

            if ((decimal)NumCk_Sl.Value <= 0)
            {
                MessageBox.Show("数量必须大于0！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCk_Sl.Select();
                return false;
            }
            if ((decimal)NumCk_Sl.Value > 9999)
            {
                MessageBox.Show("数量过大！请检查", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCk_Sl.SelectAll();
                return false;
            }
            if (NumCk_Dj.Value == DBNull.Value || NumCk_Dj.Value == null)
            {
                MessageBox.Show("单价不能为空！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCk_Dj.Select();
                return false;
            }
            if ((decimal)NumCk_Dj.Value <= 0)
            {
                MessageBox.Show("单价必须大于0！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCk_Dj.Select();
                return false;
            }
            if ((decimal)NumCk_Dj.Value > 9999)
            {
                MessageBox.Show("单价过大！请检查", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCk_Dj.SelectAll();
                return false;
            }
            return true;
        }

        private void DataAdd()
        {
            base.SubItemRow = base.MyTable.NewRow();
            // Ck_Id
            // Ck_Code
            // Yp_Code
            // Ck_Sl
            // Ck_Dj
            // Ck_Money
            // Ck_Memo
            SubItemRow["Ck_Id"] = -(myGrid1.RowCount + 1);
            SubItemRow["Ck_Code"] = DateTime.Now.ToString("yyMMdd");
            SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue + "";
            SubItemRow["Ck_Sl"] = NumCk_Sl.Value;
            SubItemRow["Ck_Dj"] = NumCk_Dj.Value;
            SubItemRow["Ck_Money"] = NumYpCk_Money.Value;
            SubItemRow["Yp_Name"] = comboYpPh1.Columns["Yp_Name"].Value.ToString();
            SubItemRow["Yp_Pzwh"] = comboYpPh1.Columns["Yp_Pzwh"].Value.ToString();
            SubItemRow["Yp_Scqy"] = comboYpPh1.Columns["Yp_Scqy"].Value.ToString();
            SubItemRow["Yp_Bzgg"] = comboYpPh1.Columns["Yp_Bzgg"].Value.ToString();
            SubItemRow["Yp_Zjdw"] = comboYpPh1.Columns["Yp_Zjdw"].Value.ToString();
            SubItemRow["Yp_Jx"] = comboYpPh1.Columns["Yp_Jx"].Value.ToString();

            //数据保存
            try
            {
                base.MyTable.Rows.Add(base.SubItemRow);
                MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                comboYpPh1.Select();
            }

            DataClear();
        }

        private void DataEdit()
        {
            SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue + "";
            SubItemRow["Ck_Sl"] = NumCk_Sl.Value;
            SubItemRow["Ck_Dj"] = NumCk_Dj.Value;
            SubItemRow["Ck_Money"] = NumYpCk_Money.Value;
            SubItemRow["Yp_Name"] = comboYpPh1.Columns["Yp_Name"].Value.ToString();
            SubItemRow["Yp_Pzwh"] = comboYpPh1.Columns["Yp_Pzwh"].Value.ToString();
            SubItemRow["Yp_Scqy"] = comboYpPh1.Columns["Yp_Scqy"].Value.ToString();
            SubItemRow["Yp_Bzgg"] = comboYpPh1.Columns["Yp_Bzgg"].Value.ToString();
            SubItemRow["Yp_Zjdw"] = comboYpPh1.Columns["Yp_Zjdw"].Value.ToString();
            SubItemRow["Yp_Jx"] = comboYpPh1.Columns["Yp_Jx"].Value.ToString();
            comboYpPh1.Select();
            MyTransmitTxt.OnSetText("");

            DataClear();
        }
        private void BtnAddCb_Click(object sender, EventArgs e)
        {
            DataClear();
        }
        private void BtnEditCb_Click(object sender, EventArgs e)
        {
            if (myGrid1.RowCount == 0) return;
            if (ZbStateCheck("从表修改") == false) return;
            SubItemRow = ((DataRowView)MyCm.List[myGrid1.Row]).Row;
            DataShow(SubItemRow);
        }
        private void BtnSaveCb_Click(object sender, EventArgs e)
        {
            if (ZbStateCheck("从表修改") == false) return;
            NumCk_Sl.ValidateText();
            NumCk_Dj.ValidateText();
            if (DataCheck())
            {
                if (NumCk_Dj.Value != DBNull.Value && NumCk_Sl.Value != DBNull.Value)
                    NumYpCk_Money.Value = Math.Round((decimal)NumCk_Dj.Value * (decimal)NumCk_Sl.Value, 2);
                if (subInsert == true)
                {
                    this.DataAdd();
                }
                else
                {
                    this.DataEdit();
                }
            }
        }
        private void BtnDelCb_Click(object sender, EventArgs e)
        {
            if (myGrid1.RowCount == 0) return;
            if (ZbStateCheck("从表修改") == false) return;
            if (MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1) == DialogResult.OK)
            {
                try
                {
                    SubItemRow = ((DataRowView)MyCm.List[myGrid1.Row]).Row;
                    _bllYkCk2.Delete(int.Parse(SubItemRow["Ck_Id"].ToString()));
                    myGrid1.Delete();
                    MyTransmitTxt.OnSetText("");
                    if (this.Insert == false)
                    {
                        ZbRow["Ck_Money"] = Ck_Money;
                        _mdlYkCk1.Ck_Money = Ck_Money;
                        _bllYkCk1.Update(_mdlYkCk1);
                    }
                }
                catch (Exception ex)
                {
                    if (ex.Message.ToString() == "索引 -1 不是为负数，就是大于行数。")
                    {
                        MessageBox.Show("未选中数据行", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

            }
        }
        private void BtnDelAll_Click(object sender, EventArgs e)
        {
            if (myGrid1.RowCount == 0) return;
            if (ZbStateCheck("从表修改") == false) return;
            if (MessageBox.Show("确认是否删除所有记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1) == DialogResult.OK)
            {
                try
                {
                    foreach (DataRow SubItemRow in base.MyTable.Rows)
                    {
                        _bllYkCk2.Delete(int.Parse(SubItemRow["Ck_Id"].ToString()));
                        DeleteTrac(int.Parse(SubItemRow["Ck_Id"].ToString()));
                    }
                    base.MyTable.Rows.Clear();
                    base.MyTable.AcceptChanges();
                    MyTransmitTxt.OnSetText("");
                    if (this.Insert == false)
                    {
                        ZbRow["Ck_Money"] = Ck_Money;
                        _mdlYkCk1.Ck_Money = Ck_Money;
                        _bllYkCk1.Update(_mdlYkCk1);
                    }
                }
                catch (Exception ex)
                {
                    if (ex.Message.ToString() == "索引 -1 不是为负数，就是大于行数。")
                    {
                        MessageBox.Show("未选中数据行", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

            }
        }
        private void NumCk_Dj_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)(Keys.Return))
                BtnSaveCb.PerformClick();
        }
        private void comboYpPh1_RowChange(object sender, EventArgs e)
        {
            if (comboYpPh1.SelectedValue != null)
            {
                NumCk_Dj.Value = comboYpPh1.Columns["Yp_Xsdj"].Value;
            }
            else
            {
                NumCk_Dj.Value = 0;
            }
        }

        #endregion
        private void toolStrip2_Paint(object sender, PaintEventArgs e)
        {
            if ((sender as ToolStrip).RenderMode == ToolStripRenderMode.ManagerRenderMode)
            {
                Rectangle rect = new Rectangle(0, 0, (sender as ToolStrip).Width - 1, (sender as ToolStrip).Height - 1);
                e.Graphics.SetClip(rect);
            }
        }


        #region 输入控件事件
        private void NumHyZk_ValueChanged(object sender, EventArgs e)
        {
            // 当会员折扣变化时，自动计算实际付款金额
            if (_frmInit && Convert.ToDecimal(NumCkMoney.Value) > 0)
            {
                NumJsMoney.Value = Convert.ToDecimal(NumCkMoney.Value) * Convert.ToDecimal(NumHyZk.Value);
            }
        }

        private void Txtdrug_trac_codg_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (!StringExtension.IsNullOrEmpty(TxtTraceCode.Text))
                {
                    string traceCode = TxtTraceCode.Text.Trim();
                    TxtTraceCode.Text = "";
                    Task.Factory.StartNew(() =>
                    {
                        bool result = YdTraceCode.TraceCodeCk.ScanTraceCode(traceCode, MyTable, tracTable, _mdlYkCk1.Ck_Code);
                    });
                }
            }
        }





        #endregion

        #endregion


    }
}
