﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CA0EE4E7-B36C-4366-84EA-9F5DE3D5C61E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YdBusiness</RootNamespace>
    <AssemblyName>YdBusiness</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="SunnyUI, Version=3.1.9.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.3.1.9\lib\net40\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.1.2.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.Common.3.1.2\lib\net40\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="处方管理\Cf_Sh1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="处方管理\Cf_Sh1.Designer.cs">
      <DependentUpon>Cf_Sh1.cs</DependentUpon>
    </Compile>
    <Compile Include="收银交班\Jb_Sy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="收银交班\Jb_Sy.Designer.cs">
      <DependentUpon>Jb_Sy.cs</DependentUpon>
    </Compile>
    <Compile Include="药品调价\Yp_Tj.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="药品调价\Yp_Tj.Designer.cs">
      <DependentUpon>Yp_Tj.cs</DependentUpon>
    </Compile>
    <Compile Include="订单验收\Ys_Order1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="订单验收\Ys_Order1.Designer.cs">
      <DependentUpon>Ys_Order1.cs</DependentUpon>
    </Compile>
    <Compile Include="订单验收\Ys_Order2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="订单验收\Ys_Order2.Designer.cs">
      <DependentUpon>Ys_Order2.cs</DependentUpon>
    </Compile>
    <Compile Include="订单验收\Ys_Order3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="订单验收\Ys_Order3.Designer.cs">
      <DependentUpon>Ys_Order3.cs</DependentUpon>
    </Compile>
    <Compile Include="采购入库\Yf_Rk1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采购入库\Yf_Rk1.Designer.cs">
      <DependentUpon>Yf_Rk1.cs</DependentUpon>
    </Compile>
    <Compile Include="采购入库\Yf_Rk2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采购入库\Yf_Rk2.Designer.cs">
      <DependentUpon>Yf_Rk2.cs</DependentUpon>
    </Compile>
    <Compile Include="采购入库\Yf_Rk3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采购入库\Yf_Rk3.Designer.cs">
      <DependentUpon>Yf_Rk3.cs</DependentUpon>
    </Compile>
    <Compile Include="采购订单\Dd_Order1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采购订单\Dd_Order1.Designer.cs">
      <DependentUpon>Dd_Order1.cs</DependentUpon>
    </Compile>
    <Compile Include="采购订单\Dd_Order2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采购订单\Dd_Order2.Designer.cs">
      <DependentUpon>Dd_Order2.cs</DependentUpon>
    </Compile>
    <Compile Include="采购订单\Dd_Order3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采购订单\Dd_Order3.Designer.cs">
      <DependentUpon>Dd_Order3.cs</DependentUpon>
    </Compile>
    <Compile Include="销售出库\PrescriptionPreview.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="销售出库\PrescriptionPreview.Designer.cs">
      <DependentUpon>PrescriptionPreview.cs</DependentUpon>
    </Compile>
    <Compile Include="销售出库\XsCk1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="销售出库\XsCk1.Designer.cs">
      <DependentUpon>XsCk1.cs</DependentUpon>
    </Compile>
    <Compile Include="销售出库\XsCk2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="销售出库\XsCk2.Designer.cs">
      <DependentUpon>XsCk2.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Enum\Common.Enum.csproj">
      <Project>{eca72bf5-a6c2-4ecb-a80a-9723ef1098a1}</Project>
      <Name>Common.Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdControl\YdControl.csproj">
      <Project>{186009e9-7a04-4519-a696-79e57bb80b4e}</Project>
      <Name>YdControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdEnum\YdEnum.csproj">
      <Project>{2658ea9e-035b-43e4-b40f-6cebe092f702}</Project>
      <Name>YdEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdPublicFunction\YdPublicFunction.csproj">
      <Project>{dfd1bd9d-45ff-4998-99bf-2a661d038f7c}</Project>
      <Name>YdPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdResources\YdResources.csproj">
      <Project>{fd356f66-2186-4b9d-b45b-e8e7b6040a8a}</Project>
      <Name>YdResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdTraceCode\YdTraceCode.csproj">
      <Project>{924c5773-6c76-4df6-8dad-30b6a5bcb504}</Project>
      <Name>YdTraceCode</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdVar\YdVar.csproj">
      <Project>{4596a1b7-93c2-4ff7-9412-a9b49e7beb6a}</Project>
      <Name>YdVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="处方管理\Cf_Sh1.resx">
      <DependentUpon>Cf_Sh1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="收银交班\Jb_Sy.resx">
      <DependentUpon>Jb_Sy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="药品调价\Yp_Tj.resx">
      <DependentUpon>Yp_Tj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="订单验收\Ys_Order1.resx">
      <DependentUpon>Ys_Order1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="订单验收\Ys_Order2.resx">
      <DependentUpon>Ys_Order2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="订单验收\Ys_Order3.resx">
      <DependentUpon>Ys_Order3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采购入库\Yf_Rk1.resx">
      <DependentUpon>Yf_Rk1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采购入库\Yf_Rk2.resx">
      <DependentUpon>Yf_Rk2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采购入库\Yf_Rk3.resx">
      <DependentUpon>Yf_Rk3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采购订单\Dd_Order1.resx">
      <DependentUpon>Dd_Order1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采购订单\Dd_Order2.resx">
      <DependentUpon>Dd_Order2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采购订单\Dd_Order3.resx">
      <DependentUpon>Dd_Order3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="销售出库\PrescriptionPreview.resx">
      <DependentUpon>PrescriptionPreview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="销售出库\XsCk1.resx">
      <DependentUpon>XsCk1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="销售出库\XsCk2.resx">
      <DependentUpon>XsCk2.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="Resources\查询.png" />
    <EmbeddedResource Include="Rpt\药店销售小票.mrt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增加.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\初始化.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\保存.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\一键清费.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除对应.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\修改.png" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>