﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_Ck1.cs
*
* 功 能： N/A
* 类 名： DalYk_Ck1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
namespace IDAL
{
	/// <summary>
	/// 接口层BllYk_Ck1
	/// </summary>
	public interface IDalYk_Ck1
	{
		#region  成员方法
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		bool Exists(string Ck_Code);
		/// <summary>
		/// 增加一条数据
		/// </summary>
		bool Add(Model.MdlYk_Ck1 model);
		/// <summary>
		/// 更新一条数据
		/// </summary>
		bool Update(Model.MdlYk_Ck1 model);
		/// <summary>
		/// 删除一条数据
		/// </summary>
		bool Delete(string Ck_Code);
		bool DeleteList(string Ck_Codelist);
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		Model.MdlYk_Ck1 GetModel(string Ck_Code);
		Model.MdlYk_Ck1 DataRowToModel(DataRow row);
		/// <summary>
		/// 获得数据列表
		/// </summary>
		DataSet GetList(string strWhere);
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		DataSet GetList(int Top, string strWhere, string filedOrder);
		int GetRecordCount(string strWhere);
		DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex);
		/// <summary>
		/// 根据分页获得数据列表
		/// </summary>
		//DataSet GetList(int PageSize,int PageIndex,string strWhere);
		#endregion  成员方法
		#region  MethodEx

		/// <summary>
		/// 得到最大编码
		/// </summary>
		string MaxCode(int length);

		#endregion  MethodEx
		#region  MethodEx
		/// <summary>
		/// 获取收费明细（按支付方式分组统计）
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <returns>收费明细数据集</returns>
		DataSet GetPayDetail(string czyCode, DateTime startTime, DateTime endTime);

		/// <summary>
		/// 获取销售明细
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <returns>销售明细数据集</returns>
		DataSet GetSaleDetail(string czyCode, DateTime startTime, DateTime endTime);

		/// <summary>
		/// 更新销售记录的交班编码
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <param name="jbCode">交班编码</param>
		/// <returns>更新的记录数</returns>
		int UpdateJbCode(string czyCode, DateTime startTime, DateTime endTime, string jbCode);

		/// <summary>
		/// 获取收费明细（按支付方式分组统计）- 根据交班编码
		/// </summary>
		/// <param name="jbCode">交班编码</param>
		/// <returns>收费明细数据集</returns>
		DataSet GetPayDetailByJbCode(string jbCode);

		/// <summary>
		/// 获取销售明细 - 根据条件
		/// </summary>
		/// <param name="strWhere">条件</param>
		/// <returns>销售明细数据集</returns>
		DataSet GetSaleDetail(string strWhere);
		/// <summary>
		/// 结算单据
		/// </summary>
		/// <param name="ckCode">单据编码</param>
		/// <returns>是否成功</returns>
		bool Complete(string ckCode);
		/// <summary>
		/// 取消结算单据
		/// </summary>
		/// <param name="ckCode">单据编码</param>
		/// <returns>是否成功</returns>
		bool CancelSettle(string ckCode);
		#endregion  MethodEx
	}
}
