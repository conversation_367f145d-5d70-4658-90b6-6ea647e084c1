using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Model;

namespace YdBaseDict
{
    public partial class Zd_Yp_Dict1 : Common.BaseForm.BaseDict21
    {
        BLL.BllZd_Yp1 _bllZdYp1 = new BllZd_Yp1();
        BLL.BllZd_Yp2 _bllZdYp2 = new BllZd_Yp2();

        public Zd_Yp_Dict1()
        {
            InitializeComponent();
        }

        private void Zd_Yp_Dict1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            base.BaseTreeView = treeView1;
            this.FormInit();
            this.SubDataInit("");
            Thread thread = new Thread(this.TreeInit);
            thread.IsBackground = true;
            thread.Start();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }

        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("药品编码", "Xl_Code", 120, "中", "", false);
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("规格", "Yp_Bzgg", 120, "左", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 80, "左", "", false);
            myGrid1.Init_Column("单位", "Yp_Zjdw", 60, "中", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 150, "左", "", false);
            myGrid1.Init_Column("生产厂家", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("OTC", "Yp_Otc", 80, "中", "", false);
            myGrid1.Init_Column("备注", "Yp_Memo", 200, "左", "", false);
            myGrid1.Splits[0].DisplayColumns["Yp_Otc"].FetchStyle = true;
            myGrid1.FetchCellStyle += YdPublicFunction.GridFunction.IsOtc_FetchCellStyle;
            myGrid1.AllowSort = true;
        }

        private void TreeInit()
        {
            treeView1.BeginInvoke(new Action(() => { treeView1.Nodes.Clear(); }));
            TreeNode myRoot = new TreeNode();
            myRoot.Tag = new Common.MdlTreeNode() { SubItemCount = _bllZdYp2.GetRecordCount(""), ItemName = "药品分类", ItemCode = "" };
            myRoot.Name = "";
            myRoot.Text = "药品分类(" + ((Common.MdlTreeNode)myRoot.Tag).SubItemCount + ")";
            myRoot.ImageIndex = 0;
            treeView1.Invoke(new Action<TreeNode>(p => { treeView1.Nodes.Add(p); }), myRoot);
            DataTable dt = _bllZdYp1.GetList("").Tables[0];
            foreach (DataRow nodeRow in dt.Rows)
            {
                //获取二级表记录数
                int itemCount;
                itemCount = _bllZdYp2.GetRecordCount("Dl_Code='" + nodeRow["Dl_Code"].ToString() + "'");
                TreeNode node = new TreeNode();
                node.Name = nodeRow["Dl_Code"].ToString();
                node.Tag = new Common.MdlTreeNode() { SubItemCount = itemCount, ItemName = nodeRow["Dl_Name"].ToString(), ItemCode = nodeRow["Dl_Code"].ToString() };
                node.Text = nodeRow["Dl_Name"].ToString() + "(" + itemCount + ")";
                node.ImageIndex = 1;
                node.SelectedImageIndex = 2;
                treeView1.Invoke(new Action<TreeNode>(p => { myRoot.Nodes.Add(p); }), node);
            }

            treeView1.BeginInvoke(new Action<TreeNode>(p => { treeView1.SelectedNode = p; }), myRoot);
            treeView1.BeginInvoke(new Action(() => { treeView1.ExpandAll(); }));
            treeView1.BeginInvoke(new Action(() => { treeView1.Select(); }));
        }
        //从表数据初始化
        private void SubDataInit(string Dl_Code)
        {
            if (Dl_Code == "")
            {
                base.MyTable = _bllZdYp2.GetList("").Tables[0];
            }
            else
            {
                base.MyTable = _bllZdYp2.GetList("Dl_Code='" + Dl_Code + "'").Tables[0];
            }

            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Xl_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Xl_Code";
        }

        private void CheckMasterOrSub(TreeNode node)
        {
            FormInit();
            if (node == treeView1.TopNode)
            {
                base.IsMainTb = true;
                SubDataInit("");
            }
            else
            {
                base.IsMainTb = false;
                SubDataInit(node.Name);
            }
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            Zd_Yp_Dict2 vform = new Zd_Yp_Dict2(base.Insert, base.MyRow, base.MyTable, treeView1.SelectedNode.Name == "0" ? "" : treeView1.SelectedNode.Name);
            vform.MyTreeNode = treeView1.SelectedNode;
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.ShowDialog();
        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (base.IsMainTb == true)
            {
                MessageBox.Show("不能删除药品分类！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else
            {
                if (MessageBox.Show("是否删除药品名称：" + this.myGrid1.Columns["Yp_Name"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                    return;
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;

                TreeNode node = treeView1.Nodes.Find(base.MyRow["Dl_Code"].ToString(), true)[0];
                Common.TreeNodeHelper.UpdateNode(node, -1);
                Common.TreeNodeHelper.UpdateNode(treeView1.TopNode, -1);

                _bllZdYp2.Delete(base.MyRow["Xl_Code"].ToString());
            }

            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void DataRefresh()
        {
            if (base.IsMainTb == true)
            {
                SubDataInit("");
                Thread thread = new Thread(this.TreeInit);
                thread.IsBackground = true;
                thread.Start();
            }
            else
            {
                SubDataInit(treeView1.SelectedNode.Name);
            }
        }

        #endregion

        #region 控件动作
        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void myGrid1_DoubleClick(object sender, EventArgs e)
        {
            this.DataEdit(false);
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("Yp_Name+Yp_Jc+Yp_Pzwh", TxtFilter.Text.Trim());
        }

        private void treeView1_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            CheckMasterOrSub(e.Node);
        }

        #endregion
    }
}
