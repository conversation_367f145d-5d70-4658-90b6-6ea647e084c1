﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_Ck1.cs
*
* 功 能： N/A
* 类 名： DalYk_Ck1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
using System.Collections.Generic; // Added for List
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalYk_Ck1
	/// </summary>
	public partial class DalYk_Ck1 : IDalYk_Ck1
	{
		public DalYk_Ck1()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Ck_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Yk_Ck1");
			strSql.Append(" where Ck_Code=@Ck_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Code", SqlDbType.Char,9)          };
			parameters[0].Value = Ck_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlYk_Ck1 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Yk_Ck1(");
			strSql.Append("Yd_Code,Ck_Date,Ck_Code,Czy_Code,Czy_Name,Gk_Name,Gk_Tele,Gk_Sfzh,Ck_Money,Hy_Zk,Js_Money,Ck_Memo,Ck_Cf,Cf_Code,Cf_Cfys,Cf_Dw,Cf_State,Cf_Memo,Sf_Date,Sfys_Code,Sfys_Name,Sfys_Memo,Sc_Finish,Ck_Sl,Pay_Type,ImageData,Jb_Code,Ck_Ok,PrescriptionFileName)");
			strSql.Append(" values (");
			strSql.Append("@Yd_Code,@Ck_Date,@Ck_Code,@Czy_Code,@Czy_Name,@Gk_Name,@Gk_Tele,@Gk_Sfzh,@Ck_Money,@Hy_Zk,@Js_Money,@Ck_Memo,@Ck_Cf,@Cf_Code,@Cf_Cfys,@Cf_Dw,@Cf_State,@Cf_Memo,@Sf_Date,@Sfys_Code,@Sfys_Name,@Sfys_Memo,@Sc_Finish,@Ck_Sl,@Pay_Type,@ImageData,@Jb_Code,@Ck_Ok,@PrescriptionFileName)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yd_Code", SqlDbType.Char,4),
					new SqlParameter("@Ck_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@Czy_Code", SqlDbType.Char,3),
					new SqlParameter("@Czy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Gk_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Gk_Tele", SqlDbType.VarChar,11),
					new SqlParameter("@Gk_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Ck_Money", SqlDbType.Decimal,5),
					new SqlParameter("@Hy_Zk", SqlDbType.Decimal,5),
					new SqlParameter("@Js_Money", SqlDbType.Decimal,5),
					new SqlParameter("@Ck_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Ck_Cf", SqlDbType.Bit,1),
					new SqlParameter("@Cf_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Cf_Cfys", SqlDbType.VarChar,50),
					new SqlParameter("@Cf_Dw", SqlDbType.VarChar,50),
					new SqlParameter("@Cf_State", SqlDbType.Char,1),
					new SqlParameter("@Cf_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Sf_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Sfys_Code", SqlDbType.Char,3),
					new SqlParameter("@Sfys_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Sfys_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Ck_Sl", SqlDbType.Decimal,5),
					new SqlParameter("@Pay_Type", SqlDbType.VarChar,10),
					new SqlParameter("@ImageData", SqlDbType.Image),
					new SqlParameter("@Jb_Code", SqlDbType.Char,100),
					new SqlParameter("@Ck_Ok", SqlDbType.VarChar,50),
					new SqlParameter("@PrescriptionFileName", SqlDbType.VarChar,500)};

			parameters[0].Value = model.Yd_Code;
			parameters[1].Value = model.Ck_Date;
			parameters[2].Value = model.Ck_Code;
			parameters[3].Value = model.Czy_Code;
			parameters[4].Value = model.Czy_Name;
			parameters[5].Value = model.Gk_Name;
			parameters[6].Value = model.Gk_Tele;
			parameters[7].Value = model.Gk_Sfzh;
			parameters[8].Value = model.Ck_Money;
			parameters[9].Value = model.Hy_Zk;
			parameters[10].Value = model.Js_Money;
			parameters[11].Value = model.Ck_Memo;
			parameters[12].Value = Common.Tools.IsValueNull(model.Ck_Cf);
			parameters[13].Value = Common.Tools.IsValueNull(model.Cf_Code);
			parameters[14].Value = Common.Tools.IsValueNull(model.Cf_Cfys);
			parameters[15].Value = Common.Tools.IsValueNull(model.Cf_Dw);
			parameters[16].Value = Common.Tools.IsValueNull(model.Cf_State);
			parameters[17].Value = Common.Tools.IsValueNull(model.Cf_Memo);
			parameters[18].Value = Common.Tools.IsValueNull(model.Sf_Date);
			parameters[19].Value = Common.Tools.IsValueNull(model.Sfys_Code);
			parameters[20].Value = Common.Tools.IsValueNull(model.Sfys_Name);
			parameters[21].Value = Common.Tools.IsValueNull(model.Sfys_Memo);
			parameters[22].Value = Common.Tools.IsValueNull(model.Sc_Finish);
			parameters[23].Value = Common.Tools.IsValueNull(model.Ck_Sl);
			parameters[24].Value = Common.Tools.IsValueNull(model.Pay_Type);
			parameters[25].Value = Common.Tools.IsValueNull(model.ImageData);
			parameters[26].Value = Common.Tools.IsValueNull(model.Jb_Code);
			parameters[27].Value = Common.Tools.IsValueNull(model.Ck_Ok);
			parameters[28].Value = Common.Tools.IsValueNull(model.PrescriptionFileName);

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlYk_Ck1 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Yk_Ck1 set ");
			strSql.Append("Yd_Code=@Yd_Code,");
			strSql.Append("Ck_Date=@Ck_Date,");
			strSql.Append("Czy_Code=@Czy_Code,");
			strSql.Append("Czy_Name=@Czy_Name,");
			strSql.Append("Gk_Name=@Gk_Name,");
			strSql.Append("Gk_Tele=@Gk_Tele,");
			strSql.Append("Gk_Sfzh=@Gk_Sfzh,");
			strSql.Append("Ck_Money=@Ck_Money,");
			strSql.Append("Hy_Zk=@Hy_Zk,");
			strSql.Append("Js_Money=@Js_Money,");
			strSql.Append("Ck_Memo=@Ck_Memo,");
			strSql.Append("Ck_Cf=@Ck_Cf,");
			strSql.Append("Cf_Code=@Cf_Code,");
			strSql.Append("Cf_Cfys=@Cf_Cfys,");
			strSql.Append("Cf_Dw=@Cf_Dw,");
			strSql.Append("Cf_State=@Cf_State,");
			strSql.Append("Cf_Memo=@Cf_Memo,");
			strSql.Append("Sf_Date=@Sf_Date,");
			strSql.Append("Sfys_Code=@Sfys_Code,");
			strSql.Append("Sfys_Name=@Sfys_Name,");
			strSql.Append("Sfys_Memo=@Sfys_Memo,");
			strSql.Append("Sc_Finish=@Sc_Finish,");
			strSql.Append("Ck_Sl=@Ck_Sl,");
			strSql.Append("Pay_Type=@Pay_Type,");
			strSql.Append("ImageData=@ImageData,");
			strSql.Append("Jb_Code=@Jb_Code,");
			strSql.Append("Ck_Ok=@Ck_Ok,");
			strSql.Append("PrescriptionFileName=@PrescriptionFileName");
			strSql.Append(" where Ck_Code=@Ck_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yd_Code", SqlDbType.Char,4),
					new SqlParameter("@Ck_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Czy_Code", SqlDbType.Char,3),
					new SqlParameter("@Czy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Gk_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Gk_Tele", SqlDbType.VarChar,11),
					new SqlParameter("@Gk_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Ck_Money", SqlDbType.Decimal,5),
					new SqlParameter("@Hy_Zk", SqlDbType.Decimal,5),
					new SqlParameter("@Js_Money", SqlDbType.Decimal,5),
					new SqlParameter("@Ck_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Ck_Cf", SqlDbType.Bit,1),
					new SqlParameter("@Cf_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Cf_Cfys", SqlDbType.VarChar,50),
					new SqlParameter("@Cf_Dw", SqlDbType.VarChar,50),
					new SqlParameter("@Cf_State", SqlDbType.Char,1),
					new SqlParameter("@Cf_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Sf_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Sfys_Code", SqlDbType.Char,3),
					new SqlParameter("@Sfys_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Sfys_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Ck_Sl", SqlDbType.Decimal,5),
					new SqlParameter("@Pay_Type", SqlDbType.VarChar,10),
					new SqlParameter("@ImageData", SqlDbType.Image),
					new SqlParameter("@Jb_Code", SqlDbType.Char,100),
					new SqlParameter("@Ck_Ok", SqlDbType.VarChar,50),
					new SqlParameter("@PrescriptionFileName", SqlDbType.VarChar,500),
					new SqlParameter("@Ck_Code", SqlDbType.Char,9)};
			parameters[0].Value = model.Yd_Code;
			parameters[1].Value = model.Ck_Date;
			parameters[2].Value = model.Czy_Code;
			parameters[3].Value = model.Czy_Name;
			parameters[4].Value = model.Gk_Name;
			parameters[5].Value = model.Gk_Tele;
			parameters[6].Value = model.Gk_Sfzh;
			parameters[7].Value = model.Ck_Money;
			parameters[8].Value = model.Hy_Zk;
			parameters[9].Value = model.Js_Money;
			parameters[10].Value = model.Ck_Memo;
			parameters[11].Value = Common.Tools.IsValueNull(model.Ck_Cf);
			parameters[12].Value = Common.Tools.IsValueNull(model.Cf_Code);
			parameters[13].Value = Common.Tools.IsValueNull(model.Cf_Cfys);
			parameters[14].Value = Common.Tools.IsValueNull(model.Cf_Dw);
			parameters[15].Value = Common.Tools.IsValueNull(model.Cf_State);
			parameters[16].Value = Common.Tools.IsValueNull(model.Cf_Memo);
			parameters[17].Value = Common.Tools.IsValueNull(model.Sf_Date);
			parameters[18].Value = Common.Tools.IsValueNull(model.Sfys_Code);
			parameters[19].Value = Common.Tools.IsValueNull(model.Sfys_Name);
			parameters[20].Value = Common.Tools.IsValueNull(model.Sfys_Memo);
			parameters[21].Value = Common.Tools.IsValueNull(model.Sc_Finish);
			parameters[22].Value = Common.Tools.IsValueNull(model.Ck_Sl);
			parameters[23].Value = Common.Tools.IsValueNull(model.Pay_Type);
			parameters[24].Value = Common.Tools.IsValueNull(model.ImageData);
			parameters[25].Value = Common.Tools.IsValueNull(model.Jb_Code);
			parameters[26].Value = Common.Tools.IsValueNull(model.Ck_Ok);
			parameters[27].Value = Common.Tools.IsValueNull(model.PrescriptionFileName);
			parameters[28].Value = model.Ck_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Ck_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Yk_Ck1 ");
			strSql.Append(" where Ck_Code=@Ck_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Code", SqlDbType.Char,9)          };
			parameters[0].Value = Ck_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Ck_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Yk_Ck1 ");
			strSql.Append(" where Ck_Code in (" + Ck_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_Ck1 GetModel(string Ck_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Yd_Code,Ck_Date,Ck_Code,Czy_Code,Czy_Name,Gk_Name,Gk_Tele,Gk_Sfzh,Ck_Money,Hy_Zk,Js_Money,Ck_Memo,Ck_Cf,Cf_Code,Cf_Cfys,Cf_Dw,Cf_State,Cf_Memo,Sf_Date,Sfys_Code,Sfys_Name,Sfys_Memo,Sc_Finish,Ck_Sl,Pay_Type,ImageData,Jb_Code,Ck_Ok,PrescriptionFileName from Yk_Ck1 ");
			strSql.Append(" where Ck_Code=@Ck_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Code", SqlDbType.Char,9)          };
			parameters[0].Value = Ck_Code;

			Model.MdlYk_Ck1 model = new Model.MdlYk_Ck1();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_Ck1 DataRowToModel(DataRow row)
		{
			Model.MdlYk_Ck1 model = new Model.MdlYk_Ck1();
			if (row != null)
			{
				if (row["Yd_Code"] != null)
				{
					model.Yd_Code = row["Yd_Code"].ToString();
				}
				if (row["Ck_Date"] != null && row["Ck_Date"].ToString() != "")
				{
					model.Ck_Date = DateTime.Parse(row["Ck_Date"].ToString());
				}
				if (row["Ck_Code"] != null)
				{
					model.Ck_Code = row["Ck_Code"].ToString();
				}
				if (row["Czy_Code"] != null)
				{
					model.Czy_Code = row["Czy_Code"].ToString();
				}
				if (row["Czy_Name"] != null)
				{
					model.Czy_Name = row["Czy_Name"].ToString();
				}
				if (row["Gk_Name"] != null)
				{
					model.Gk_Name = row["Gk_Name"].ToString();
				}
				if (row["Gk_Tele"] != null)
				{
					model.Gk_Tele = row["Gk_Tele"].ToString();
				}
				if (row["Gk_Sfzh"] != null)
				{
					model.Gk_Sfzh = row["Gk_Sfzh"].ToString();
				}
				if (row["Ck_Money"] != null && row["Ck_Money"].ToString() != "")
				{
					model.Ck_Money = decimal.Parse(row["Ck_Money"].ToString());
				}
				if (row["Hy_Zk"] != null && row["Hy_Zk"].ToString() != "")
				{
					model.Hy_Zk = decimal.Parse(row["Hy_Zk"].ToString());
				}
				if (row["Js_Money"] != null && row["Js_Money"].ToString() != "")
				{
					model.Js_Money = decimal.Parse(row["Js_Money"].ToString());
				}
				if (row["Ck_Memo"] != null)
				{
					model.Ck_Memo = row["Ck_Memo"].ToString();
				}
				if (row["Ck_Cf"] != null && row["Ck_Cf"].ToString() != "")
				{
					if ((row["Ck_Cf"].ToString() == "1") || (row["Ck_Cf"].ToString().ToLower() == "true"))
					{
						model.Ck_Cf = true;
					}
					else
					{
						model.Ck_Cf = false;
					}
				}
				if (row["Cf_Code"] != null)
				{
					model.Cf_Code = row["Cf_Code"].ToString();
				}
				if (row["Cf_Cfys"] != null)
				{
					model.Cf_Cfys = row["Cf_Cfys"].ToString();
				}
				if (row["Cf_Dw"] != null)
				{
					model.Cf_Dw = row["Cf_Dw"].ToString();
				}
				if (row["Cf_State"] != null)
				{
					model.Cf_State = row["Cf_State"].ToString();
				}
				if (row["Cf_Memo"] != null)
				{
					model.Cf_Memo = row["Cf_Memo"].ToString();
				}
				if (row["Sf_Date"] != null && row["Sf_Date"].ToString() != "")
				{
					model.Sf_Date = DateTime.Parse(row["Sf_Date"].ToString());
				}
				if (row["Sfys_Code"] != null)
				{
					model.Sfys_Code = row["Sfys_Code"].ToString();
				}
				if (row["Sfys_Name"] != null)
				{
					model.Sfys_Name = row["Sfys_Name"].ToString();
				}
				if (row["Sfys_Memo"] != null)
				{
					model.Sfys_Memo = row["Sfys_Memo"].ToString();
				}
				if (row["Sc_Finish"] != null && row["Sc_Finish"].ToString() != "")
				{
					if ((row["Sc_Finish"].ToString() == "1") || (row["Sc_Finish"].ToString().ToLower() == "true"))
					{
						model.Sc_Finish = true;
					}
					else
					{
						model.Sc_Finish = false;
					}
				}
				if (row["Ck_Sl"] != null && row["Ck_Sl"].ToString() != "")
				{
					model.Ck_Sl = decimal.Parse(row["Ck_Sl"].ToString());
				}
				if (row["Pay_Type"] != null)
				{
					model.Pay_Type = row["Pay_Type"].ToString();
				}
				if (row["ImageData"] != null && row["ImageData"].ToString() != "")
				{
					model.ImageData = (byte[])row["ImageData"];
				}
				if (row["Jb_Code"] != null)
				{
					model.Jb_Code = row["Jb_Code"].ToString();
				}
				if (row["Ck_Ok"] != null)
				{
					model.Ck_Ok = row["Ck_Ok"].ToString();
				}
				if (row["PrescriptionFileName"] != null)
				{
					model.PrescriptionFileName = row["PrescriptionFileName"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Yd_Code,Ck_Date,Ck_Code,Czy_Code,Czy_Name,Gk_Name,Gk_Tele,Gk_Sfzh,Ck_Money,Hy_Zk,Js_Money,Ck_Memo,Ck_Cf,Cf_Code,Cf_Cfys,Cf_Dw,Cf_State,Cf_Memo,Sf_Date,Sfys_Code,Sfys_Name,Sfys_Memo,Sc_Finish,Ck_Sl,Pay_Type,ImageData,Jb_Code,Ck_Ok,PrescriptionFileName ");
			strSql.Append(" FROM Yk_Ck1 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Yd_Code,Ck_Date,Ck_Code,Czy_Code,Czy_Name,Gk_Name,Gk_Tele,Gk_Sfzh,Ck_Money,Hy_Zk,Js_Money,Ck_Memo,Ck_Cf,Cf_Code,Cf_Cfys,Cf_Dw,Cf_State,Cf_Memo,Sf_Date,Sfys_Code,Sfys_Name,Sfys_Memo,Sc_Finish,Ck_Sl,Pay_Type,ImageData,Jb_Code,Ck_Ok,PrescriptionFileName ");
			strSql.Append(" FROM Yk_Ck1 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Yk_Ck1 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Ck_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Yk_Ck1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yk_Ck1";
			parameters[1].Value = "Ck_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 获取收费明细（按支付方式分组统计）
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <returns>收费明细数据集</returns>
		public DataSet GetPayDetail(string czyCode, DateTime startTime, DateTime endTime)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("Pay_Type, ");
			strSql.Append("SUM(c.Js_Money) as Js_Money ");
			strSql.Append("FROM Yk_Ck1 c ");
			strSql.Append("WHERE c.Czy_Code = @Czy_Code ");
			strSql.Append("AND c.Ck_Date >= @StartTime ");
			strSql.Append("AND c.Ck_Date <= @EndTime ");
			strSql.Append("GROUP BY c.Pay_Type ");
			strSql.Append("ORDER BY c.Pay_Type");

			SqlParameter[] parameters = {
				new SqlParameter("@Czy_Code", SqlDbType.Char, 3),
				new SqlParameter("@StartTime", SqlDbType.DateTime),
				new SqlParameter("@EndTime", SqlDbType.DateTime)
			};
			parameters[0].Value = czyCode;
			parameters[1].Value = startTime;
			parameters[2].Value = endTime;

			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 获取销售明细
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <returns>销售明细数据集</returns>
		public DataSet GetSaleDetail(string czyCode, DateTime startTime, DateTime endTime)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("c.Ck_Date, ");
			strSql.Append("c.Ck_Code, ");
			strSql.Append("c.Czy_Name, ");
			strSql.Append("c.Js_Money, ");
			strSql.Append("c.Pay_Type, ");
			strSql.Append("c.Ck_Sl ");
			strSql.Append("FROM Yk_Ck1 c ");
			strSql.Append("inner join (select Ck_Code From Zd_Yp4 group by Ck_Code) p ON c.Ck_Code = p.Ck_Code ");
			strSql.Append("WHERE c.Czy_Code = @Czy_Code ");
			strSql.Append("AND c.Ck_Date >= @StartTime ");
			strSql.Append("AND c.Ck_Date <= @EndTime ");
			strSql.Append("ORDER BY c.Ck_Date DESC");

			SqlParameter[] parameters = {
				new SqlParameter("@Czy_Code", SqlDbType.Char, 3),
				new SqlParameter("@StartTime", SqlDbType.DateTime),
				new SqlParameter("@EndTime", SqlDbType.DateTime)
			};
			parameters[0].Value = czyCode;
			parameters[1].Value = startTime;
			parameters[2].Value = endTime;

			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 更新销售记录的交班编码
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <param name="jbCode">交班编码</param>
		/// <returns>更新的记录数</returns>
		public int UpdateJbCode(string czyCode, DateTime startTime, DateTime endTime, string jbCode)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("UPDATE Yk_Ck1 SET Jb_Code = @Jb_Code ");
			strSql.Append("WHERE Czy_Code = @Czy_Code ");
			strSql.Append("AND Ck_Date >= @StartTime ");
			strSql.Append("AND Ck_Date <= @EndTime ");
			strSql.Append("AND (Jb_Code IS NULL OR Jb_Code = '')");
			strSql.Append("AND Exists (Select 1 From Zd_Yp4 Where Zd_Yp4.Ck_Code=Yk_Ck1.Ck_Code)");

			SqlParameter[] parameters = {
				new SqlParameter("@Jb_Code", SqlDbType.Char, 100),
				new SqlParameter("@Czy_Code", SqlDbType.Char, 3),
				new SqlParameter("@StartTime", SqlDbType.DateTime),
				new SqlParameter("@EndTime", SqlDbType.DateTime)
			};
			parameters[0].Value = jbCode;
			parameters[1].Value = czyCode;
			parameters[2].Value = startTime;
			parameters[3].Value = endTime;

			return Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 获取收费明细（按支付方式分组统计）- 根据交班编码
		/// </summary>
		/// <param name="jbCode">交班编码</param>
		/// <returns>收费明细数据集</returns>
		public DataSet GetPayDetailByJbCode(string jbCode)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("c.Pay_Type, ");
			strSql.Append("p.Pay_Name, ");
			strSql.Append("SUM(c.Js_Money) as Js_Money ");
			strSql.Append("FROM Yk_Ck1 c ");
			strSql.Append("LEFT JOIN Zd_Pay p ON c.Pay_Type = p.Pay_Code ");
			strSql.Append("WHERE c.Jb_Code = @Jb_Code ");
			strSql.Append("GROUP BY c.Pay_Type, p.Pay_Name ");
			strSql.Append("ORDER BY c.Pay_Type");

			SqlParameter[] parameters = {
				new SqlParameter("@Jb_Code", SqlDbType.Char, 13)
			};
			parameters[0].Value = jbCode;

			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 获取销售明细 - 根据条件
		/// </summary>
		/// <param name="strWhere">条件</param>
		/// <returns>销售明细数据集</returns>
		public DataSet GetSaleDetail(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("c.Ck_Date, ");
			strSql.Append("c.Ck_Code, ");
			strSql.Append("c.Czy_Name, ");
			strSql.Append("c.Js_Money, ");
			strSql.Append("c.Pay_Type, ");
			strSql.Append("c.Ck_Sl ");
			strSql.Append("FROM Yk_Ck1 c ");
			strSql.Append("WHERE EXISTS (SELECT 1 FROM Zd_Yp4 WHERE Zd_Yp4.Ck_Code = c.Ck_Code) ");
			if (!string.IsNullOrEmpty(strWhere))
			{
				strSql.Append("AND " + strWhere);
			}
			strSql.Append("ORDER BY c.Ck_Date DESC");


			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = DateTime.Now.ToString("yyMMdd") + Common.WinFormVar.Var.DbHelper.F_MaxCode($"SELECT MAX(RIGHT(Ck_Code,3)) FROM Yk_Ck1 WHERE LEFT(Ck_Code,6)='" + DateTime.Now.ToString("yyMMdd") + "'", length - 6);
			return max;
		}

		public bool Complete(string ckCode)
		{
			List<string> strList = new List<string>();
			List<IDataParameter[]> parametersList = new List<IDataParameter[]>();

			try
			{
				StringBuilder strSql1 = new StringBuilder();
				strSql1.Append("UPDATE Yk_Ck1 SET Ck_Ok=@Ck_Ok WHERE Ck_Code=@Ck_Code");
				SqlParameter[] parameters1 = {
					new SqlParameter("@Ck_Ok", SqlDbType.VarChar, 50),
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters1[0].Value = "已结算";
				parameters1[1].Value = ckCode;

				strList.Add(strSql1.ToString());
				parametersList.Add(parameters1);

				StringBuilder strSql2 = new StringBuilder();
				strSql2.Append("UPDATE Zd_Yp3 SET Yp_Count = Yp_Count - ck2.Total_Ck_Sl ");
				strSql2.Append("FROM Zd_Yp3 t3 ");
				strSql2.Append("INNER JOIN (SELECT Yp_Code, SUM(Ck_Sl) AS Total_Ck_Sl FROM Yk_Ck2 WHERE Ck_Code = @Ck_Code GROUP BY Yp_Code) ck2 ");
				strSql2.Append("ON t3.Yp_Code = ck2.Yp_Code");

				SqlParameter[] parameters2 = {
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters2[0].Value = ckCode;

				strList.Add(strSql2.ToString());
				parametersList.Add(parameters2);

				StringBuilder strSql3 = new StringBuilder();
				strSql3.Append("UPDATE Zd_Yp2 SET Xl_Count = Xl_Count - ck2.Total_Ck_Sl ");
				strSql3.Append("FROM Zd_Yp2 t2 ");
				strSql3.Append("INNER JOIN (SELECT Xl_Code, SUM(Ck_Sl) AS Total_Ck_Sl FROM Yk_Ck2,Zd_Yp3 WHERE Ck_Code = @Ck_Code AND Zd_Yp3.Yp_Code = Yk_Ck2.Yp_Code GROUP BY Xl_Code) ck2 ");
				strSql3.Append("ON t2.Xl_Code = ck2.Xl_Code");

				SqlParameter[] parameters3 = {
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters3[0].Value = ckCode;

				strList.Add(strSql3.ToString());
				parametersList.Add(parameters3);

				StringBuilder strSql4 = new StringBuilder();
				strSql4.Append("UPDATE Zd_Yp4 SET Ck_Code=@Ck_Code, Ck_Date=GETDATE() ");
				strSql4.Append("WHERE EXISTS (");
				strSql4.Append("SELECT 1 FROM Yk_CkDrugtracinfo WHERE Yk_CkDrugtracinfo.drug_trac_codg = Zd_Yp4.Sy_Code AND Yk_CkDrugtracinfo.Ck_Code=@Ck_Code)");

				SqlParameter[] parameters4 = {
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters4[0].Value = ckCode;

				strList.Add(strSql4.ToString());
				parametersList.Add(parameters4);

				// 执行事务
				int result = Common.WinFormVar.Var.DbHelper.ExecuteSql(strList, parametersList);
				return result > 0;
			}
			catch (Exception ex)
			{
				throw ex;
			}
		}
		public bool CancelSettle(string ckCode)
		{
			List<string> strList = new List<string>();
			List<IDataParameter[]> parametersList = new List<IDataParameter[]>();

			try
			{
				StringBuilder strSql1 = new StringBuilder();
				strSql1.Append("UPDATE Yk_Ck1 SET Ck_Ok=@Ck_Ok WHERE Ck_Code=@Ck_Code");
				SqlParameter[] parameters1 = {
					new SqlParameter("@Ck_Ok", SqlDbType.VarChar, 50),
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters1[0].Value = "未结算";
				parameters1[1].Value = ckCode;

				strList.Add(strSql1.ToString());
				parametersList.Add(parameters1);

				StringBuilder strSql2 = new StringBuilder();
				strSql2.Append("UPDATE Zd_Yp3 SET Yp_Count = Yp_Count + ck2.Total_Ck_Sl ");
				strSql2.Append("FROM Zd_Yp3 t3 ");
				strSql2.Append("INNER JOIN (SELECT Yp_Code, SUM(Ck_Sl) AS Total_Ck_Sl FROM Yk_Ck2 WHERE Ck_Code = @Ck_Code GROUP BY Yp_Code) ck2 ");
				strSql2.Append("ON t3.Yp_Code = ck2.Yp_Code");

				SqlParameter[] parameters2 = {
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters2[0].Value = ckCode;

				strList.Add(strSql2.ToString());
				parametersList.Add(parameters2);

				StringBuilder strSql3 = new StringBuilder();
				strSql3.Append("UPDATE Zd_Yp2 SET Xl_Count = Xl_Count + ck2.Total_Ck_Sl ");
				strSql3.Append("FROM Zd_Yp2 t2 ");
				strSql3.Append("INNER JOIN (SELECT Xl_Code, SUM(Ck_Sl) AS Total_Ck_Sl FROM Yk_Ck2,Zd_Yp3 WHERE Ck_Code = @Ck_Code AND Zd_Yp3.Yp_Code = Yk_Ck2.Yp_Code GROUP BY Xl_Code) ck2 ");
				strSql3.Append("ON t2.Xl_Code = ck2.Xl_Code");

				SqlParameter[] parameters3 = {
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters3[0].Value = ckCode;

				strList.Add(strSql3.ToString());
				parametersList.Add(parameters3);

				StringBuilder strSql4 = new StringBuilder();
				strSql4.Append("UPDATE Zd_Yp4 SET Ck_Code=null, Ck_Date=null ");
				strSql4.Append("WHERE EXISTS (");
				strSql4.Append("SELECT 1 FROM Yk_CkDrugtracinfo WHERE Yk_CkDrugtracinfo.drug_trac_codg = Zd_Yp4.Sy_Code AND Yk_CkDrugtracinfo.Ck_Code=@Ck_Code)");

				SqlParameter[] parameters4 = {
					new SqlParameter("@Ck_Code", SqlDbType.Char, 9)
				};
				parameters4[0].Value = ckCode;

				strList.Add(strSql4.ToString());
				parametersList.Add(parameters4);

				// 执行事务
				int result = Common.WinFormVar.Var.DbHelper.ExecuteSql(strList, parametersList);
				return result > 0;
			}
			catch (Exception ex)
			{
				throw ex;
			}
		}
		#endregion  ExtensionMethod
	}
}

