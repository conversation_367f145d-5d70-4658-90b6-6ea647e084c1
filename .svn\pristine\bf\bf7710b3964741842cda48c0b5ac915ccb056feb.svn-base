﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_Rk2.cs
*
* 功 能： N/A
* 类 名： DalYk_Rk2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/8/1 9:27:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
namespace IDAL
{
	/// <summary>
	/// 接口层BllYk_Rk2
	/// </summary>
	public interface IDalYk_Rk2
	{
		#region  成员方法
		/// <summary>
		/// 得到最大ID
		/// </summary>
		int GetMaxId();
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		bool Exists(int Rk_Id);
		/// <summary>
		/// 增加一条数据
		/// </summary>
		int Add(Model.MdlYk_Rk2 model, ref string Rk_Id);
		/// <summary>
		/// 更新一条数据
		/// </summary>
		bool Update(Model.MdlYk_Rk2 model);
		/// <summary>
		/// 删除一条数据
		/// </summary>
		bool Delete(int Rk_Id);
		bool DeleteList(string Rk_Idlist);
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		Model.MdlYk_Rk2 GetModel(int Rk_Id);
		Model.MdlYk_Rk2 DataRowToModel(DataRow row);
		/// <summary>
		/// 获得数据列表
		/// </summary>
		DataSet GetList(string strWhere);
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		DataSet GetList(int Top, string strWhere, string filedOrder);
		int GetRecordCount(string strWhere);
		DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex);
		/// <summary>
		/// 根据分页获得数据列表
		/// </summary>
		//DataSet GetList(int PageSize,int PageIndex,string strWhere);
		#endregion  成员方法
		#region  MethodEx
		DataTable GetListByRkCode(string Rk_Code);

		bool UpdateYpCode(int RkId, string YpCode);
		DataTable GetFirstYpDjList(string XlCode);

		#endregion  MethodEx
	}
}
