using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace YdControl
{
    public partial class ComboCzy : MyDtComobo
    {
        public ComboCzy()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            BLL.BllZd_Czy _bllZd_Czy = new BllZd_Czy();
            this.DataView = _bllZd_Czy.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("Czy_Name", "姓名", 100, "左");
            // this.Init_Colum("Czy_Code", "编码", 60, "左");
            // this.Init_Colum("Czy_Jc", "简称", 60, "左");
            this.Init_Colum("Czy_Lb", "类别", 80, "左");
            this.DisplayMember = "Czy_Name";
            this.ValueMember = "Czy_Code";
            int width = 300;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "Czy_Code+isnull(Czy_Jc,'')+Czy_Name";
        }
    }
}
