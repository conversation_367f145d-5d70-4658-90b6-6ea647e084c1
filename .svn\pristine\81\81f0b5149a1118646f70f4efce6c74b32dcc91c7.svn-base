﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Transactions;
using System.Windows.Forms;
using BLL;
using C1.Win.C1TrueDBGrid;
using Common;
using Common.Delegate;
using Model;
using Stimulsoft.Report;

namespace YdBusiness
{
    public partial class Yf_Rk2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BllYk_Rk1 _bllRk1 = new BllYk_Rk1();
        private BllYk_Rk2 _bllRk2 = new BllYk_Rk2();
        private BllKc_Pd1 _bllKc_Pd1 = new BllKc_Pd1();
        private BllZd_Yp3 _bllZdYp3 = new BllZd_Yp3();


        //private BllCountry_YB_SpKc_Drugtracinfo _bllCountryYbSpKcDrugtracinfo = new BllCountry_YB_SpKc_Drugtracinfo();
        private Model.MdlYk_Rk1 _mdlYfRk1 = new MdlYk_Rk1();
        private decimal Rk_Money = 0;
        private DataTable tracTable;
        public Yf_Rk2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = insert;
            ZbTable = table;
            comboGys1.GotFocus += new System.EventHandler(base.InputEn);
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Yf_Rk2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert)
                Zb_Clear();
            else
                Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
        }
        private void Yf_Rk2_FormClosing(object sender, FormClosingEventArgs e)
        {
          
        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {
            base.BaseBtnDelete = BtnDelete;
            base.BaseBtnNew = BtnNew;
            base.BaseBtnSave = BtnSave;
            base.BaseBtnComplete = BtnComplete;
            base.BaseBtnPrint = BtnPrint;
            base.BaseBtnClose = BtnClose;
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;

            BtnDelete.Location = new Point(30, 1);
            BtnDeleteAll.Location = new Point(BtnDelete.Left + BtnDelete.Width + 2, 1);
            BtnNew.Location = new Point(BtnDeleteAll.Left + BtnDeleteAll.Width + 2, 1);
            BtnTraceCodeRk.Location = new Point(BtnNew.Left + BtnNew.Width + 2, 1);
            BtnSave.Location = new Point(BtnTraceCodeRk.Left + BtnTraceCodeRk.Width + 2, 1);
            BtnComplete.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);
            BtnPrint.Location = new Point(BtnComplete.Left + BtnComplete.Width + 2, 1);
            BtnTraceCode.Location = new Point(BtnPrint.Left + BtnPrint.Width + 2, 1);
            BtnClose.Location = new Point(BtnTraceCode.Left + BtnTraceCode.Width + 2, 1);

            TxtCode.Enabled = false;
            NumRkMoney.Enabled = false;
            comboGys1.Init("Kh_Use=1");
            RkDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            RkDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            RkDate.EditFormat = "yyyy-MM-dd HH:mm:ss";

            myGrid1.Init_Grid();
            myGrid1.Init_Column("名称", "Yp_Name", 180, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 130, "左", "", false);
            myGrid1.Init_Column("包装规格", "Yp_Bzgg", 100, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 220, "左", "", false);
            myGrid1.Init_Column("产品批号", "Yp_Scph", 90, "左", "", false);
            myGrid1.Init_Column("有效期", "Yp_ScDate2", 100, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("数量", "Rk_Sl", 60, "右", "0.####", false);
            myGrid1.Init_Column("追溯码数量", "traccnt", 60, "右", "0.####", false);
            myGrid1.Init_Column("采购单价", "Rk_Dj", 80, "右", "0.00####", false);
            myGrid1.Init_Column("采购金额", "Rk_Money", 100, "右", "0.00##", false);

            myGrid1.Splits[0].DisplayColumns["traccnt"].FetchStyle = true;
            myGrid1.ColumnFooters = true;
            myGrid1.AllowAddNew = true;
            NumRkMoney.CustomFormat = "###,###,##0.00####";
        }
        private void BtnState()
        {
            if (_mdlYfRk1.Rk_Ok.IsNullOrEmpty() || _mdlYfRk1.Rk_Ok == "未完成")
            {
                BtnDelete.Enabled = true;
                BtnDeleteAll.Enabled = true;
                BtnNew.Enabled = true;
                BtnSave.Enabled = true;
                BtnComplete.Enabled = true;
                BtnTraceCodeRk.Enabled = true;
                BtnPrint.Enabled = false;
                BtnClose.Enabled = true;
                ControlEnable(true);
            }
            else
            {
                BtnDelete.Enabled = false;
                BtnDeleteAll.Enabled = false;
                BtnNew.Enabled = true;
                BtnSave.Enabled = false;
                BtnComplete.Enabled = false;
                BtnTraceCodeRk.Enabled = false;
                BtnPrint.Enabled = true;
                BtnClose.Enabled = true;
                ControlEnable(false);
            }
        }
        private void ControlEnable(bool flag)
        {
            comboGys1.Enabled = flag;
            RkDate.Enabled = flag;
            // singleCrkFk_Zt1.Enabled = flag;
            TxtMemo.Enabled = flag;
            myGrid1.AllowAddNew = flag;
        }
        #endregion

        #region  显示函数
        protected override void Zb_Clear()
        {
            base.Insert = true;
            _mdlYfRk1 = new MdlYk_Rk1();
            ZbRow = ZbTable.NewRow();
            TxtCode.Text = _bllRk1.MaxCode(DateTime.Now);
            comboGys1.SelectedIndex = -1;
            RkDate.Value = DateTime.Now;
            TxtMemo.Text = "";
            ZbPictureShow("");
            comboGys1.Select();
        }
        private void Zb_Show()
        {
            _mdlYfRk1 = _bllRk1.GetModel(ZbRow["Rk_Code"] + "");
            TxtCode.Text = _mdlYfRk1.Rk_Code;
            comboGys1.SelectedValue = _mdlYfRk1.Kh_Code;
            RkDate.Value = _mdlYfRk1.Rk_Date;

            TxtMemo.Text = _mdlYfRk1.Rk_Memo;
            ZbPictureShow(_mdlYfRk1.Rk_Ok);
            comboGys1.Select();
        }
        private void ZbPictureShow(string Rk_Ok)
        {
            if (Rk_Ok == "已完成")
            {
                pictureBox1.Image = YdResources.StateRes.已完成;
            }
            if (Rk_Ok == "未完成")
            {
                pictureBox1.Image = YdResources.StateRes.未完成;
            }
            if (Rk_Ok == "")
            {
                pictureBox1.Image = YdResources.StateRes.新单;
            }

        }
        private void Cb_Show()
        {
            MyTable = _bllRk2.GetListByRkCode(_mdlYfRk1.Rk_Code);
            MyTable.Columns["traccnnt"].ReadOnly = false;
            MyTable.TableName = "明细";
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            //tracTable = _bllCountryYbSpKcDrugtracinfo.GetList($"Left(jxc_code,18)='YFRK{_mdlYfRk1.Rk_Code}'").Tables[0];
            //tracTable.Columns["id"].ReadOnly = false;
            //tracTable.Constraints.Clear();
            //DataColumn col = new DataColumn("IsCheck", System.Type.GetType("System.Boolean"));
            //col.DefaultValue = false;
            //col.ReadOnly = false;
            //tracTable.Columns.Add(col);
            DataSum("");
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此入库单已经被删除，无法继续操作!请点击新单，重新入库", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(comboGys1)) return false;
            if ((DateTime)RkDate.Value > Convert.ToDateTime("2079-06-01"))
            {
                RkDate.Select();
                MessageBox.Show("填写的入库时间超出范围，请重新输入!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }
        private bool ZbStateCheck(string state)
        {
            if (state == "录入")
            {
                if (_bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "' And Rk_Ok='已完成' ") > 0)
                {
                    MessageBox.Show("此入库单已经完成!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            if (state == "完成")
            {
                if (!this.Insert && _bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此入库单已经被删除，无法继续操作!请点击新单，重新入库", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
                if (base.MyTable.Rows.Count == 0)
                {
                    MessageBox.Show("此出库单没有明细，无法完成!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
                if (_bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "' And Rk_Ok='已完成' ") > 0)
                {
                    MessageBox.Show("此入库单已经完成!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
                //暂时用Kc_Pd1     ?
                if (_bllKc_Pd1.GetRecordCount($"  Pd_Finish=1 ") > 0)
                {
                    MessageBox.Show("正在进行盘点，请等待盘点完成后再完成！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            if (state == "从表修改")
            {
                if (_bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "' And Rk_Ok='已完成' ") > 0)
                {
                    MessageBox.Show("此入库单已经完成!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            if (state == "删除")
            {
                if (!this.Insert && _bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此入库单已经被删除，无法继续操作!请点击新单，重新入库", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
                if (_bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text + "' And Rk_Ok='已完成' ") > 0)
                {
                    MessageBox.Show("此入库单已经完成!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数
        //删除行
        protected override bool DataDeleteOne()
        {
            if (myGrid1.Row >= myGrid1.RowCount)
            {
                MessageBox.Show("请选择一条记录!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            if (base.DataDeleteOne() == true)
            {
                _bllRk2.Delete(Convert.ToInt32(SubItemRow["Rk_Id"]+""));
                MyTable.AcceptChanges();
                ////删除追溯码
                //List<DataRow> rowsToDelete = new List<DataRow>();
                //foreach (DataRow row in tracTable.AsEnumerable().Where(p => p.Field<string>("jxc_code") == $"YFRK{Rk_Code}-{Xx_Code}"))
                //{
                //    rowsToDelete.Add(row);
                //}
                //foreach (DataRow row in rowsToDelete)
                //{
                //    tracTable.Rows.Remove(row);
                //}
                //tracTable.AcceptChanges();
                //DataSum("");
                //LblTotal.Text = "∑=" + (MyTable.Rows.Count).ToString();
                //return true;
            }
            return false;
        }

        protected override bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {

            if (_mdlYfRk1 == null)
            {
                MessageBox.Show("数据尚未保存,无法删除!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            ZbRow.Delete();
            return base.DataDeleteAll(PrimaryKey, Delete);
        }

        protected override void DataNew()
        {
            myGrid1.UpdateData();
            if (base.MyTable.DataSet.HasChanges() == true)
            {
                if (_bllRk1.GetRecordCount("Rk_Code='" + TxtCode.Text.Trim() + "' and Rk_Ok='未完成'") > 0)
                {
                    if (MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.OK)
                    {
                        DataSave(true);
                    }
                }
            }

            Zb_Clear();
            BtnState();
            Cb_Show();
            comboGys1.Select();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            if (base.Insert == true) TxtCode.Text = _bllRk1.MaxCode(DateTime.Now);
            if (showMsgbox == true && ZbStateCheck("录入") == false) return false;
            DataSum("");
            if (base.Insert == true)
            {
                //增加记录
                Zb_Add();
            }
            else
            {
                //编辑记录
                Zb_Edit();
            }
            if (showMsgbox == true) MessageBox.Show("数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return true;
        }

        protected override void DataComplete()
        {
            if (ZbStateCheck("完成") == false) return;
            if (MessageBox.Show("是否完成此单据？", "提示:", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.No)
                return;
            if (DataSave(false) == false) return;
            Cb_Show();
            if (RkComplete())
            {
                _mdlYfRk1.Rk_Ok = "已完成";
                ZbRow["Rk_Ok"] = "已完成";
                ZbPictureShow("已完成");
                BtnState();
                if (MessageBox.Show("操作已完成，是否进行打印？", "提示:", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                {
                    //DataPrint();
                    MessageBox.Show("暂未开发");
                }
            }
        }

        #endregion

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            Rk_Money = MyTable.Compute("Sum(Rk_Money)", "") == DBNull.Value ? 0 : Convert.ToDecimal(MyTable.Compute("Sum(Rk_Money)", ""));
            myGrid1.Columns["Rk_Money"].FooterText = string.Format("{0:####0.00##}", Rk_Money);
            NumRkMoney.BeginInvoke(new Action<decimal>(p => { NumRkMoney.Value = p; }), Rk_Money);
            if (!_mdlYfRk1.Rk_Code.IsNullOrEmpty())
            {
                _mdlYfRk1.Rk_Money = Rk_Money;
                _bllRk1.UpdateMoney(_mdlYfRk1);
                ZbRow["Rk_Money"] = Rk_Money;
                ZbTable.AcceptChanges();
            }
        }

        private bool RkComplete()
        {
            try
            {
                using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                {
                    //修改Zd_Yp3,Rk2
                    MyTable.AsEnumerable().ToList().ForEach(p =>
                    {
                        Model.MdlZd_Yp3 mdlZd_Yp3;
                        Model.MdlYk_Rk2 mdlYk_Rk2 = new MdlYk_Rk2();
                        string strWhere = $" Xl_Code='{p["Xl_Code"] + ""}' And Yp_Scph='{p["Yp_Scph"] + ""}' And Yp_ScDate1='{p["Yp_ScDate1"] + ""}' And Yp_ScDate2='{p["Yp_ScDate2"] + ""}'";
                        if (_bllZdYp3.GetRecordCount(strWhere) == 0)
                        {
                            mdlZd_Yp3 = new MdlZd_Yp3();
                            mdlZd_Yp3.Xl_Code = p["Xl_Code"] + "";
                            mdlZd_Yp3.Yp_Code = _bllZdYp3.MaxCode(mdlZd_Yp3.Xl_Code);
                            mdlZd_Yp3.Yp_Scph = p["Yp_Scph"] + "";
                            mdlZd_Yp3.Yp_ScDate1 = Convert.ToDateTime(p["Yp_ScDate1"]);
                            mdlZd_Yp3.Yp_ScDate2 = Convert.ToDateTime(p["Yp_ScDate2"]);
                            mdlZd_Yp3.Yp_Memo = "通过入库自动添加";
                            mdlZd_Yp3.Yp_Count = Convert.ToDecimal(p["Rk_Sl"]);
                            mdlZd_Yp3.Sc_Finish = false;

                            _bllZdYp3.Add(mdlZd_Yp3);
                        }
                        else
                        {
                            mdlZd_Yp3 = Common.DataTableToList.ToModel<MdlZd_Yp3>(_bllZdYp3.GetList(strWhere).Tables[0].Rows[0]);
                            _bllZdYp3.UpdateRkSl(mdlZd_Yp3.Yp_Code, Convert.ToDecimal(p["Rk_Sl"]));
                        }

                        _bllRk2.UpdateYpCode(Convert.ToInt32(p["Rk_Id"] + ""), mdlZd_Yp3.Yp_Code);

                    });

                    //修改单据完成状态
                    _bllRk1.UpDateStatus(TxtCode.Text.Trim());
                    scope.Complete();
                    return true;           
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
          
        }

        #region 主表

        //增加记录
        private void Zb_Add()
        {
            _mdlYfRk1.Rk_Code = _bllRk1.MaxCode(DateTime.Now);
            TxtCode.Text = _mdlYfRk1.Rk_Code;
            _mdlYfRk1.Czy_Code = YdVar.Var.JsrCode;
            _mdlYfRk1.Czy_Name = YdVar.Var.UserName;
            _mdlYfRk1.Rk_Date = (DateTime)RkDate.Value;
            _mdlYfRk1.Kh_Code = comboGys1.SelectedValue.ToString();
            _mdlYfRk1.Kh_Name = comboGys1.Text;
            _mdlYfRk1.Rk_Memo = TxtMemo.Text;
            _mdlYfRk1.Rk_Money = Rk_Money;
            _mdlYfRk1.Sc_Finish = false;
            _mdlYfRk1.Rk_Ok = "未完成";
            //_mdlYfRk1.Rk_Money=   datasum里面赋值
            _bllRk1.Add(_mdlYfRk1);
            Common.DataTableToList.ToDataRow<MdlYk_Rk1>(_mdlYfRk1, ZbRow);
            ZbRow["Czy_Name"] = YdVar.Var.UserName;
            ZbTable.Rows.Add(ZbRow);
            base.Insert = false;
        }
        //编辑记录
        private void Zb_Edit()
        {
            _mdlYfRk1.Rk_Date = (DateTime)RkDate.Value;
            _mdlYfRk1.Kh_Code = comboGys1.SelectedValue.ToString();
            _mdlYfRk1.Kh_Name = comboGys1.Text;
            _mdlYfRk1.Rk_Memo = TxtMemo.Text;
            _mdlYfRk1.Rk_Money = Rk_Money;
            _mdlYfRk1.Czy_Code = YdVar.Var.JsrCode;
            _mdlYfRk1.Czy_Name = YdVar.Var.UserName;

            _bllRk1.Update(_mdlYfRk1);
            Common.DataTableToList.ToDataRow<MdlYk_Rk1>(_mdlYfRk1, ZbRow);
            ZbRow["Czy_Name"] = YdVar.Var.UserName;
        }

        #endregion

        #region 从表

        protected override void SubDataEdit()
        {
            if (ZbCheck() == false) return;
            if (ZbStateCheck("从表修改") == false) return;
            DataSave(false);
            bool subInsert;
            if ((myGrid1.Row + 1) > myGrid1.RowCount)
            {
                base.SubItemRow = base.MyTable.NewRow();
                subInsert = true;
            }
            else
            {
                base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                subInsert = false;
            }

            Yf_Rk3 f = new Yf_Rk3(_mdlYfRk1.Rk_Code, subInsert, base.SubItemRow, base.MyTable);
            // f.MyTransmitDataRow = this.MyTransmitDataRow;
            f.MyTransmitTxt = this.MyTransmitTxt;
            f.Owner = this;
            f.ShowDialog();

        }

        #endregion

        #endregion

        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DataDeleteOne();
        }
        private void BtnDeleteAll_Click(object sender, EventArgs e)
        {
           // DataDeleteAll(_mdlYfRk1.Rk_Code, _bllYfRk1.Delete);
        }
        private void BtnNew_Click(object sender, EventArgs e)
        {
            DataNew();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            DataSave(true);
        }
        private void BtnComplete_Click(object sender, EventArgs e)
        {
            DataComplete();
        }
        private void BtnTraceCode_Click(object sender, EventArgs e)
        {
            //if (myGrid1.RowCount == 0) return;
            //base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            //TraceCodeSales frm = new TraceCodeSales(base.SubItemRow, MyTable, tracTable, "药房入库");
            //frm.ShowDialog();
        }
        private void BtnTraceCodeRk_Click(object sender, EventArgs e)
        {
            //if (!ZTHisTraceCode.TraceCodeFunc.CheckMsfxConfig()) return;
            //if (ZbCheck() == false) return;
            //if (ZbStateCheck("从表修改") == false) return;
            //DataSave(false);
            //TraceCodeCRK frm = new TraceCodeCRK("药房入库", MyTable, tracTable, _mdlYfRk1.Rk_Code, _mdlYfRk1.Kh_Name);
            //frm.MyTransmitTxt = this.MyTransmitTxt;
            //frm.ShowDialog();
        }
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            DataPrint();
        }
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        private void myGrid1_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            string traccnt = grid.Columns["traccnt"].CellValue(e.Row).ToString();
            string Mz_Sl = grid.Columns["Rk_Sl"].CellValue(e.Row).ToString();
            switch (e.Column.DataColumn.DataField)
            {
                case "traccnt":
                    if (!string.IsNullOrEmpty(traccnt) && decimal.Parse(traccnt) == Math.Abs(decimal.Parse(Mz_Sl)))
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.完成16;
                    }
                    break;
            }
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.RightOfText;
        }
        #endregion

        #endregion


    }
}
