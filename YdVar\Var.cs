﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YdVar
{
    public class Var
    {
        /// <summary>
        /// 操作员编码（药店操作员使用）
        /// </summary>
        public static string JsrCode { get; set; }
        /// <summary>
        /// 人员姓名
        /// </summary>
        public static string UserName { get; set; }
        /// <summary>
        /// 角色编码
        /// </summary>
        public static string Role_Code { get; set; }
        /// <summary>
        /// 药店名称
        /// </summary>
        public static string Yd_Name { get; set; }
        /// <summary>
        /// 药店编码
        /// </summary>
        public static string Yd_Code { get; set; }
        /// <summary>
        /// 药店地址
        /// </summary>
        public static string Yd_Address { get; set; }

        /// <summary>
        /// 用户权限
        /// </summary>
        public static List<Model.MdlSysMenu1> UserPermission { get; set; }
    }
}
