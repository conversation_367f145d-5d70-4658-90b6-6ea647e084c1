﻿/**  版本信息模板在安装目录下，可自行修改。
* BllYk_Ck1.cs
*
* 功 能： N/A
* 类 名： BllYk_Ck1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using Model;
using DALFactory;
using IDAL;
namespace BLL
{
	/// <summary>
	/// BllYk_Ck1
	/// </summary>
	public partial class BllYk_Ck1
	{
		private readonly IDalYk_Ck1 dal = DataAccess.CreateDalYk_Ck1();
		public BllYk_Ck1()
		{ }
		#region  BasicMethod
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Ck_Code)
		{
			return dal.Exists(Ck_Code);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlYk_Ck1 model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlYk_Ck1 model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Ck_Code)
		{

			return dal.Delete(Ck_Code);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string Ck_Codelist)
		{
			return dal.DeleteList(Ck_Codelist);
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_Ck1 GetModel(string Ck_Code)
		{

			return dal.GetModel(Ck_Code);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
		public Model.MdlYk_Ck1 GetModelByCache(string Ck_Code)
		{

			string CacheKey = "MdlYk_Ck1Model-" + Ck_Code;
			object objModel = Common.DataCache.GetCache(CacheKey);
			if (objModel == null)
			{
				try
				{
					objModel = dal.GetModel(Ck_Code);
					if (objModel != null)
					{
						int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
						Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
					}
				}
				catch { }
			}
			return (Model.MdlYk_Ck1)objModel;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			return dal.GetList(Top, strWhere, filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlYk_Ck1> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlYk_Ck1> DataTableToList(DataTable dt)
		{
			List<Model.MdlYk_Ck1> modelList = new List<Model.MdlYk_Ck1>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				Model.MdlYk_Ck1 model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage(strWhere, orderby, startIndex, endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
		//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod
		/// <summary>
		/// 获取收费明细（按支付方式分组统计）
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <returns>收费明细数据集</returns>
		public DataSet GetPayDetail(string czyCode, DateTime startTime, DateTime endTime)
		{
			return dal.GetPayDetail(czyCode, startTime, endTime);
		}

		/// <summary>
		/// 获取销售明细
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <returns>销售明细数据集</returns>
		public DataSet GetSaleDetail(string czyCode, DateTime startTime, DateTime endTime)
		{
			return dal.GetSaleDetail(czyCode, startTime, endTime);
		}

		/// <summary>
		/// 更新销售记录的交班编码
		/// </summary>
		/// <param name="czyCode">操作员编码</param>
		/// <param name="startTime">开始时间</param>
		/// <param name="endTime">结束时间</param>
		/// <param name="jbCode">交班编码</param>
		/// <returns>更新的记录数</returns>
		public int UpdateJbCode(string czyCode, DateTime startTime, DateTime endTime, string jbCode)
		{
			return dal.UpdateJbCode(czyCode, startTime, endTime, jbCode);
		}

		/// <summary>
		/// 获取收费明细（按支付方式分组统计）- 根据交班编码
		/// </summary>
		/// <param name="jbCode">交班编码</param>
		/// <returns>收费明细数据集</returns>
		public DataSet GetPayDetailByJbCode(string jbCode)
		{
			return dal.GetPayDetailByJbCode(jbCode);
		}

		/// <summary>
		/// 获取销售明细 - 根据条件
		/// </summary>
		/// <param name="strWhere">条件</param>
		/// <returns>销售明细数据集</returns>
		public DataSet GetSaleDetail(string strWhere)
		{
			return dal.GetSaleDetail(strWhere);
		}

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			return dal.MaxCode(length);
		}
		public bool Complete(string ckCode)
		{
			return dal.Complete(ckCode);
		}
		/// <summary>
		/// 取消结算单据
		/// </summary>
		/// <param name="ckCode">单据编码</param>
		/// <returns>是否成功</returns>
		public bool CancelSettle(string ckCode)
		{
			return dal.CancelSettle(ckCode);
		}
		#endregion  ExtensionMethod
	}
}

