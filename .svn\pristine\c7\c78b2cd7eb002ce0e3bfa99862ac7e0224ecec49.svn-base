﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Common;
using DbProviderFactory;
using Stimulsoft.Report;
using 药店管理系统.Properties;

namespace 药店管理系统
{
    public partial class LoginForm : Common.BaseForm.LoginBase
    {
        Common.Register reg = new Register();

        public LoginForm()
        {
            InitializeComponent();
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            SystemUpdate();
            Thread thread;
            thread = new Thread(this.SysInit);
            thread.IsBackground = true;
            thread.Start();
        }

        #region 初始化函数

        private void SysInit()
        {
            base.SetCtlEnable(false);
            this.ParaInit();
            if (this.TestConn() == true)
            {
                DBUpdate.AddSqlScript();
                if (DBUpdate.DBNeedUpdate())
                {
                    base.SetText("正在更新数据库结构，请耐心等待不要退出...");
                    DBUpdate.DbUpdate(Settings.Default.DbPwd, Settings.Default.DbIP, Settings.Default.DbName);
                }
                base.SetText("初始化系统配置");
                base.SetCtlEnable(true);
                base.ShowLoginInfo();
                base.SetText("数据库连接成功,请登录");
            }
        }

        private void SystemUpdate()
        {
            // AutoUpdate auto = new AutoUpdate();
            // auto.AutoUpdaterStarter("http://*************:6548/AutoUpdaterStarter.Xml", "软件更新"); 
        }
        /// <summary>
        /// 系统各种参数初始化
        /// </summary>
        private void ParaInit()
        {
            base.SetText("正在初始化系统本地参数");
            if (System.Deployment.Application.ApplicationDeployment.IsNetworkDeployed == false)
            {
                //运行程序
                Common.WinFormVar.Var.ParaPath = Application.StartupPath;
            }
            else
            {
                //发布运行
                Common.WinFormVar.Var.ParaPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData) + "\\" + Application.ProductName;
                if (!System.IO.Directory.Exists(Common.WinFormVar.Var.ParaPath + "\\data")) System.IO.Directory.CreateDirectory(Common.WinFormVar.Var.ParaPath + "\\data");
                if (!System.IO.File.Exists(Common.WinFormVar.Var.ParaPath + "\\System.ini")) System.IO.File.Copy(".\\system.ini", Common.WinFormVar.Var.ParaPath + "\\System.ini");
                if (!System.IO.File.Exists(Common.WinFormVar.Var.ParaPath + "\\data\\Conf.dat")) System.IO.File.Copy(".\\data\\Conf.dat", Common.WinFormVar.Var.ParaPath + "\\data\\Conf.dat");
            }

            Common.WinFormVar.Var.IniFileHelper = new INIFileHelper(Common.WinFormVar.Var.ParaPath + "\\System.ini");
            PersistenceProperty pp = new PersistenceProperty();
            pp.DatabaseType = DatabaseType.SQLite;
            pp.ConnectionString = "Data Source=" + Common.WinFormVar.Var.ParaPath + "\\data\\Conf.dat;Version=3;";
            Common.WinFormVar.Var.ConfigDbHelper = DataAccessFactory.CreateDataAccess(pp);


            pp = new PersistenceProperty();
            pp.DatabaseType = DatabaseType.SQLite;
            pp.ConnectionString = "Data Source=" + Application.StartupPath + "\\data\\Conn.dat;Version=3;";
            Common.WinFormVar.Var.ConnDbHelper = DataAccessFactory.CreateDataAccess(pp);


            if (Settings.Default.DebugLb == "正式")
            {
                DataTable dt = Common.WinFormVar.Var.DbHelper.Query("select * from SqlCon").Tables[0];
                Settings.Default.DbIP = dt.Rows[0]["Db_Ip"].ToString();
                Settings.Default.DbName = dt.Rows[0]["Db_Name"].ToString();
                Settings.Default.DbId = dt.Rows[0]["Db_Id"].ToString();
                Settings.Default.DbPwd = dt.Rows[0]["Db_Pwd"].ToString();
            }
            StiConfig.LoadLocalization(".\\zh-CHS.xml");
        }
        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private bool TestConn()
        {
            base.SetText("正在连接数据库服务器,请稍后");
            PersistenceProperty pp = new PersistenceProperty();

            switch (Settings.Default.DAL)
            {
                case "SQLServerDAL":
                    pp.DatabaseType = DatabaseType.MSSQLServer;
                    pp.ConnectionString = "Data Source=" + Settings.Default.DbIP + ";Initial Catalog=" + Settings.Default.DbName + ";Persist Security Info=True;User Id=" + Settings.Default.DbId + ";Pwd=" + Settings.Default.DbPwd + "";

                    break;
                default:
                    pp.DatabaseType = DatabaseType.MSSQLServer;
                    pp.ConnectionString = "";
                    break;
            }
            Common.WinFormVar.Var.DbHelper = DataAccessFactory.CreateDataAccess(pp);

            Common.WinFormVar.Var.DALPath = Settings.Default.DAL;
            if (Common.WinFormVar.Var.DbHelper.TestConn() == false)
            {
                base.SetText("数据库服务器连接失败");
                return false;
            }
            return true;

        }

        #endregion

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            BLL.BllZd_Czy bllZd_Czy = new BLL.BllZd_Czy();
            Model.MdlZd_Czy mdlZd_Czy = new Model.MdlZd_Czy();
            BLL.BllZd_Yd bllZd_Yd = new BLL.BllZd_Yd();
            //检查操作员编码是否存在
            if (bllZd_Czy.GetRecordCount("Czy_Code='" + TxtLoginName.Text + "'") == 0)
            {
                SetText("该操作员编码不存在！");
                return;
            }
            // if (base.realPassword == "" || base.realPassword != TxtPassWord.Text)
            if (base.realPassword == "")
            {
                base.realPassword = TxtPassWord.Text;
            }
            string hashPassword = YdPublicFunction.Utilities.CalculateMd5Hash(realPassword);
            //检查密码是否正确
            if (bllZd_Czy.GetRecordCount("Czy_Code='" + TxtLoginName.Text + "' and Czy_HashPwd='" + hashPassword + "'") != 1)
            {
                SetText("密码不正确！");
                return;
            }

            mdlZd_Czy = bllZd_Czy.GetModel(TxtLoginName.Text);
            DataRow ydrow = bllZd_Yd.GetList(1, "", "Yd_Code").Tables[0].Rows[0];

            YdVar.Var.JsrCode = mdlZd_Czy.Czy_Code; // 设置操作员编码
            YdVar.Var.UserName = mdlZd_Czy.Czy_Name;
            YdVar.Var.Role_Code = mdlZd_Czy.RoleCode; // 固定角色编码
            YdVar.Var.Yd_Name = ydrow["Yd_Name"] + "";
            YdVar.Var.Yd_Code = ydrow["Yd_Code"] + "";
            YdVar.Var.Yd_Address = ydrow["Yd_Address"] + "";
            YdVar.Var.UserPermission = YdPublicFunction.Permission.GetUserPermission(YdVar.Var.Role_Code);


            foreach (InputLanguage il in InputLanguage.InstalledInputLanguages)
            {
                if (il.LayoutName == "搜狗拼音输入法")
                {
                    Common.WinFormVar.Var.InputMethod = il;
                }
            }

            Common.WinFormVar.Var.InputType = Common.Enum.InPutEnum.PinYin;

            base.SaveLoginInfo();
            YdPara.PublicConfig.ReadConfig();
            this.DialogResult = DialogResult.OK;
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void TxtLoginName_Validated(object sender, EventArgs e)
        {
            BLL.BllZd_Czy bllZd_Czy = new BLL.BllZd_Czy();
            if (TxtLoginName.Enabled == false) return;
            Model.MdlZd_Czy mdlZd_Czy = new Model.MdlZd_Czy();
            mdlZd_Czy = bllZd_Czy.GetModel(TxtLoginName.Text);
            if (mdlZd_Czy != null)
            {
                TxtName.Text = mdlZd_Czy.Czy_Name;
            }
            else
            {
                TxtName.Text = "";
            }
        }
    }
}
