﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_Ck2.cs
*
* 功 能： N/A
* 类 名： DalYk_Ck2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-08-01 14:22:51   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalYk_Ck2
	/// </summary>
	public partial class DalYk_Ck2 : IDalYk_Ck2
	{
		public DalYk_Ck2()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("Ck_Id", "Yk_Ck2");
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Ck_Id)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Yk_Ck2");
			strSql.Append(" where Ck_Id=@Ck_Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4)         };
			parameters[0].Value = Ck_Id;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlYk_Ck2 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Yk_Ck2(");
			strSql.Append("Ck_Code,Yp_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Ck_Code,@Yp_Code,@Ck_Sl,@Ck_Dj,@Ck_Money,@Ck_Memo)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@Yp_Code", SqlDbType.Char,11),
					new SqlParameter("@Ck_Sl", SqlDbType.Decimal,9),
					new SqlParameter("@Ck_Dj", SqlDbType.Decimal,9),
					new SqlParameter("@Ck_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Ck_Memo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Ck_Code;
			parameters[1].Value = model.Yp_Code;
			parameters[2].Value = model.Ck_Sl;
			parameters[3].Value = model.Ck_Dj;
			parameters[4].Value = model.Ck_Money;
			parameters[5].Value = Common.Tools.IsValueNull(model.Ck_Memo);

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlYk_Ck2 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Yk_Ck2 set ");
			strSql.Append("Ck_Code=@Ck_Code,");
			strSql.Append("Yp_Code=@Yp_Code,");
			strSql.Append("Ck_Sl=@Ck_Sl,");
			strSql.Append("Ck_Dj=@Ck_Dj,");
			strSql.Append("Ck_Money=@Ck_Money,");
			strSql.Append("Ck_Memo=@Ck_Memo");
			strSql.Append(" where Ck_Id=@Ck_Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@Yp_Code", SqlDbType.Char,11),
					new SqlParameter("@Ck_Sl", SqlDbType.Decimal,9),
					new SqlParameter("@Ck_Dj", SqlDbType.Decimal,9),
					new SqlParameter("@Ck_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Ck_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Ck_Id", SqlDbType.Int,4)};
			parameters[0].Value = model.Ck_Code;
			parameters[1].Value = model.Yp_Code;
			parameters[2].Value = model.Ck_Sl;
			parameters[3].Value = model.Ck_Dj;
			parameters[4].Value = model.Ck_Money;
			parameters[5].Value = Common.Tools.IsValueNull(model.Ck_Memo);
			parameters[6].Value = model.Ck_Id;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Ck_Id)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Yk_Ck2 ");
			strSql.Append(" where Ck_Id=@Ck_Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4)         };
			parameters[0].Value = Ck_Id;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Ck_Idlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Yk_Ck2 ");
			strSql.Append(" where Ck_Id in (" + Ck_Idlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_Ck2 GetModel(int Ck_Id)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Ck_Id,Ck_Code,Yp_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo from Yk_Ck2 ");
			strSql.Append(" where Ck_Id=@Ck_Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4)         };
			parameters[0].Value = Ck_Id;

			Model.MdlYk_Ck2 model = new Model.MdlYk_Ck2();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_Ck2 DataRowToModel(DataRow row)
		{
			Model.MdlYk_Ck2 model = new Model.MdlYk_Ck2();
			if (row != null)
			{
				if (row["Ck_Id"] != null && row["Ck_Id"].ToString() != "")
				{
					model.Ck_Id = int.Parse(row["Ck_Id"].ToString());
				}
				if (row["Ck_Code"] != null)
				{
					model.Ck_Code = row["Ck_Code"].ToString();
				}
				if (row["Yp_Code"] != null)
				{
					model.Yp_Code = row["Yp_Code"].ToString();
				}
				if (row["Ck_Sl"] != null && row["Ck_Sl"].ToString() != "")
				{
					model.Ck_Sl = decimal.Parse(row["Ck_Sl"].ToString());
				}
				if (row["Ck_Dj"] != null && row["Ck_Dj"].ToString() != "")
				{
					model.Ck_Dj = decimal.Parse(row["Ck_Dj"].ToString());
				}
				if (row["Ck_Money"] != null && row["Ck_Money"].ToString() != "")
				{
					model.Ck_Money = decimal.Parse(row["Ck_Money"].ToString());
				}
				if (row["Ck_Memo"] != null)
				{
					model.Ck_Memo = row["Ck_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Yk_Ck2.Ck_Id,Ck_Code,Yk_Ck2.Yp_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo ");
			strSql.Append(" ,y2.Yp_Name,y3.Yp_Scph,y3.Yp_ScDate1,y3.Yp_ScDate2,y3.Yp_Count,y2.Yp_Zjgg,y2.Yp_Zjdw,y2.Yp_Scqy,y2.Yp_Pzwh ");
			strSql.Append(" ,y2.Yp_Bzgg,y2.Yp_Jx,y2.Yp_Otc,d1.traccnt");
			strSql.Append(" FROM Yk_Ck2 ");
			strSql.Append(" INNER JOIN Zd_Yp3 y3 ON y3.Yp_Code = Yk_Ck2.Yp_Code ");
			strSql.Append(" INNER JOIN Zd_Yp2 y2 ON y3.Xl_Code = y2.Xl_Code ");
			strSql.Append("LEFT JOIN (SELECT Ck_Id,COUNT(1)traccnt FROM Yk_CkDrugtracinfo GROUP BY Ck_Id)d1 ON d1.Ck_Id=Yk_Ck2.Ck_Id");

			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Ck_Id,Ck_Code,Yp_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo ");
			strSql.Append(" FROM Yk_Ck2 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Yk_Ck2 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Ck_Id desc");
			}
			strSql.Append(")AS Row, T.*  from Yk_Ck2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yk_Ck2";
			parameters[1].Value = "Ck_Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

