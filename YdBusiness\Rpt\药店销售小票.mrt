﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="3">
      <YdXs Ref="2" type="DataTableSource" isKey="true">
        <Alias>YdXs</Alias>
        <Columns isList="true" count="9">
          <value>Yp_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Gys,System.String</value>
          <value>Yp_Gg,System.String</value>
          <value>Yp_Ph,System.String</value>
          <value>Yp_Xsj,System.String</value>
          <value>Yp_Zkj,System.String</value>
          <value>Yp_Money,System.String</value>
          <value>Yp_Yxq,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>YdXs</Name>
        <NameInSource>YdXs</NameInSource>
      </YdXs>
      <Xs Ref="3" type="DataTableSource" isKey="true">
        <Alias>Xs</Alias>
        <Columns isList="true" count="11">
          <value>Yd_Name,System.String</value>
          <value>Gk_Code,System.String</value>
          <value>Ck_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Zjgg,System.String</value>
          <value>Yp_Scqy,System.String</value>
          <value>Yp_Scph,System.String</value>
          <value>Yp_Xsdj,System.String</value>
          <value>Yp_Code,System.String</value>
          <value>Xs_Sl,System.String</value>
          <value>money,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>Xs</Name>
        <NameInSource>Xs</NameInSource>
      </Xs>
      <明细 Ref="4" type="DataTableSource" isKey="true">
        <Alias>明细</Alias>
        <Columns isList="true" count="19">
          <value>Ck_Id,System.Int32</value>
          <value>Ck_Code,System.String</value>
          <value>Yp_Code,System.String</value>
          <value>Ck_Sl,System.Decimal</value>
          <value>Ck_Dj,System.Decimal</value>
          <value>Ck_Money,System.Decimal</value>
          <value>Ck_Memo,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Scph,System.String</value>
          <value>Yp_ScDate1,System.DateTime</value>
          <value>Yp_ScDate2,System.DateTime</value>
          <value>Yp_Count,System.Decimal</value>
          <value>Yp_Zjgg,System.String</value>
          <value>Yp_Zjdw,System.String</value>
          <value>Yp_Scqy,System.String</value>
          <value>Yp_Pzwh,System.String</value>
          <value>Yp_Bzgg,System.String</value>
          <value>Yp_Jx,System.String</value>
          <value>traccnt,System.Int32</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>明细</Name>
        <NameInSource>明细</NameInSource>
      </明细>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="17">
      <value>,药店名称,药店名称,System.String,,False,False</value>
      <value>,单号,单号,System.String,,False,False</value>
      <value>,日期,日期,System.String,,False,False</value>
      <value>,本次积分,本次积分,System.String,,False,False</value>
      <value>,现积分,现积分,System.String,,False,False</value>
      <value>,医保,医保,System.String,,False,False</value>
      <value>,收款,收款,System.String,,False,False</value>
      <value>,找零,找零,System.String,,False,False</value>
      <value>,收银员,收银员,System.String,,False,False</value>
      <value>,电话,电话,System.String,,False,False</value>
      <value>,地址,地址,System.String,,False,False</value>
      <value>,时间,时间,System.String,,False,False</value>
      <value>,操作员,操作员,System.String,,False,False</value>
      <value>,客户,客户,System.String,,False,False</value>
      <value>,积分,积分,System.String,,False,False</value>
      <value>,合计金额,合计金额,System.String,,False,False</value>
      <value>,结算金额,结算金额,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="5" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,5,3.4</ClientRectangle>
          <Components isList="true" count="16">
            <Text3 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,5.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>60dfe7c6f9ca482b9040b62b794a6b1a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>单号：{单号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.4,5.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>4178af756ef24b60b59e545ffda6e2cb</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>日期：{日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text22 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.1,5,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>f3ba953c5d854b078fc4260a50637587</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>-----------------------------------------------------</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text15 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,0.4,2.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>时间：{时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text9 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,2.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>操作员：{操作员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text9>
            <Text10 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0.8,2.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>客户：{客户}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text10>
            <Text31 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,5,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>5ebd81b462fd4ac894fe9715ffc67f8b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>-----------------------------------------------------</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text16 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>商品ID</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text16>
            <Text17 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,1.8,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>92bdbc6a084443e9a6504243ab5e0ef1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>品名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text17>
            <Text19 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,2.2,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>64288217dd3641f8b57966d032cb75f2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text19>
            <Text20 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.2,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>7725c4dc874242bfabcfa4c9a47483a5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text20>
            <Text21 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,1.8,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>1135bc3de0244af5a1cf60548de08283</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>批号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text21>
            <Text23 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.6,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>8527c1b5865b402885a791d39dfda4fa</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text23>
            <Text24 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,2.6,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>a916e9893ec64abdbbc74d1a3839ee49</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text24>
            <Text25 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,2.6,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>2b304acca0b243c18dd42cadc39decd7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text25>
            <Text1 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,6</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{药店名称}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </ReportTitleBand1>
        <DataBand1 Ref="23" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.6,5,1.5</ClientRectangle>
          <Components isList="true" count="9">
            <Text27 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.4,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Yp_Zjgg}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text27>
            <Text28 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.4,1.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Yp_Scqy}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text28>
            <Text29 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,0,1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Yp_Scph}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text29>
            <Text8 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text8>
            <Text32 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Yp_Code}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text32>
            <Text26 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,0.8,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Ck_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>0.00#####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
            </Text26>
            <Text2 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.8,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Ck_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>0.######</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
            </Text2>
            <Text34 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,5,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>bb8d94225a8c4aa0ae292931397425c8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>-----------------------------------------------------</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text30 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,1.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="5" />
              <Parent isRef="23" />
              <Text>{明细.Ck_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="CustomFormat" isKey="true">
                <StringFormat>0.00#####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="36" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.9,5,2.7</ClientRectangle>
          <Components isList="true" count="9">
            <Text13 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,5,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>ee917026a51d4cd39b71c9c2e2bc429c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>-----------------------------------------------------</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text5 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.3,3.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>{合计金额}</Text>
              <TextBrush>Black</TextBrush>
            </Text5>
            <Text6 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.3,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>合计金额:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text6>
            <Text14 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.7,3.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>{结算金额}</Text>
              <TextBrush>Black</TextBrush>
            </Text14>
            <Text33 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>结算金额:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text33>
            <Text7 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.1,5.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>be415efbe30d49ba8c28fcec566dffe4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>祝您健康！</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text7>
            <Text11 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.5,5.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>1c3b39e9eac64b6297413c4257340617</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>健康咨询热线:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text11>
            <Text12 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.9,5.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>1e38a4e20df24d57961f2000c9dbe340</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>药品为特殊商品，售出概不退换</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text12>
            <Text18 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,5.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7</Font>
              <Guid>f2cb159f56d543e58eadba443dccd2dd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="5" />
              <Parent isRef="36" />
              <Text>地址：{地址}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text18>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>016e1b681f0340e19a306e6aa892d02a</Guid>
          <Name>ReportSummaryBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>02afbf12edcb4530b97328e145b4fe1d</Guid>
      <Margins>0,0,0.2,0.2</Margins>
      <Name>Page1</Name>
      <PageHeight>12</PageHeight>
      <PageWidth>5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="46" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="47" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药店销售小票(250804)</ReportAlias>
  <ReportChanged>8/4/2025 3:24:01 PM</ReportChanged>
  <ReportCreated>4/12/2019 9:27:21 AM</ReportCreated>
  <ReportFile>E:\开发代码\05药品追溯\药店管理系统\YdBusiness\Rpt\药店销售小票.mrt</ReportFile>
  <ReportGuid>8fadfe165d17494a8b65c866d9368808</ReportGuid>
  <ReportName>药店销售小票(250804)</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>