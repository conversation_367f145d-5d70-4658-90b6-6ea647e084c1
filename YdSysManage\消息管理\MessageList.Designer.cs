namespace YdSysManage
{
    partial class MessageList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.myGrid1 = new CustomControl.MyGrid();
            this.lblTotal = new System.Windows.Forms.Label();
            this.scbMessgeStatus = new CustomControl.MySingleComobo();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 300F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.myGrid1, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.scbMessgeStatus, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblTotal, 1, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1200, 700);
            this.tableLayoutPanel1.TabIndex = 0;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.tableLayoutPanel1.SetColumnSpan(this.myGrid1, 3);
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 29);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(1200, 671);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 1;
            this.myGrid1.Xmlpath = null;
            this.myGrid1.DoubleClick += new System.EventHandler(this.myGrid1_DoubleClick);
            // 
            // lblTotal
            // 
            this.lblTotal.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.lblTotal.AutoSize = true;
            this.lblTotal.ForeColor = System.Drawing.Color.Maroon;
            this.lblTotal.Location = new System.Drawing.Point(153, 7);
            this.lblTotal.Name = "lblTotal";
            this.lblTotal.Size = new System.Drawing.Size(84, 14);
            this.lblTotal.TabIndex = 2;
            this.lblTotal.Text = "共 0 条消息";
            // 
            // scbMessgeStatus
            // 
            this.scbMessgeStatus.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.scbMessgeStatus.Captain = "消息状态";
            this.scbMessgeStatus.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.scbMessgeStatus.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.scbMessgeStatus.CaptainWidth = 69F;
            this.scbMessgeStatus.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.scbMessgeStatus.ItemHeight = 18;
            this.scbMessgeStatus.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.scbMessgeStatus.Location = new System.Drawing.Point(3, 3);
            this.scbMessgeStatus.MaximumSize = new System.Drawing.Size(10000, 23);
            this.scbMessgeStatus.MinimumSize = new System.Drawing.Size(0, 20);
            this.scbMessgeStatus.Name = "scbMessgeStatus";
            this.scbMessgeStatus.ReadOnly = false;
            this.scbMessgeStatus.Size = new System.Drawing.Size(144, 23);
            this.scbMessgeStatus.TabIndex = 3;
            this.scbMessgeStatus.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.scbMessgeStatus.SelectedValueChanged += new System.EventHandler(this.scbMessgeStatus_SelectedValueChanged);
            // 
            // MessageList
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "MessageList";
            this.Text = "消息管理";
            this.Load += new System.EventHandler(this.MessageList_Load);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private CustomControl.MyGrid myGrid1;
        private System.Windows.Forms.Label lblTotal;
        private CustomControl.MySingleComobo scbMessgeStatus;
    }
}