﻿/**  版本信息模板在安装目录下，可自行修改。
* DalKc_Pd1.cs
*
* 功 能： N/A
* 类 名： DalKc_Pd1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalKc_Pd1
	/// </summary>
	public partial class DalKc_Pd1:IDalKc_Pd1
	{
		public DalKc_Pd1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Pd_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Kc_Pd1");
			strSql.Append(" where Pd_Code=@Pd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Pd_Code", SqlDbType.Char,9)			};
			parameters[0].Value = Pd_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlKc_Pd1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Kc_Pd1(");
			strSql.Append("Pd_Code,Pd_Month,Czy_Code,Czy_Name,Pd_Date,Pd_Memo,Pd_Finish)");
			strSql.Append(" values (");
			strSql.Append("@Pd_Code,@Pd_Month,@Czy_Code,@Czy_Name,@Pd_Date,@Pd_Memo,@Pd_Finish)");
			SqlParameter[] parameters = {
					new SqlParameter("@Pd_Code", SqlDbType.Char,9),
					new SqlParameter("@Pd_Month", SqlDbType.SmallDateTime),
					new SqlParameter("@Czy_Code", SqlDbType.Char,3),
					new SqlParameter("@Czy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Pd_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Pd_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Pd_Finish", SqlDbType.Bit,1)};
			parameters[0].Value = model.Pd_Code;
			parameters[1].Value = model.Pd_Month;
			parameters[2].Value = model.Czy_Code;
			parameters[3].Value = model.Czy_Name;
			parameters[4].Value = model.Pd_Date;
			parameters[5].Value = model.Pd_Memo;
			parameters[6].Value = model.Pd_Finish;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlKc_Pd1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Kc_Pd1 set ");
			strSql.Append("Pd_Month=@Pd_Month,");
			strSql.Append("Czy_Code=@Czy_Code,");
			strSql.Append("Czy_Name=@Czy_Name,");
			strSql.Append("Pd_Date=@Pd_Date,");
			strSql.Append("Pd_Memo=@Pd_Memo,");
			strSql.Append("Pd_Finish=@Pd_Finish");
			strSql.Append(" where Pd_Code=@Pd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Pd_Month", SqlDbType.SmallDateTime),
					new SqlParameter("@Czy_Code", SqlDbType.Char,3),
					new SqlParameter("@Czy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Pd_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Pd_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Pd_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Pd_Code", SqlDbType.Char,9)};
			parameters[0].Value = model.Pd_Month;
			parameters[1].Value = model.Czy_Code;
			parameters[2].Value = model.Czy_Name;
			parameters[3].Value = model.Pd_Date;
			parameters[4].Value = model.Pd_Memo;
			parameters[5].Value = model.Pd_Finish;
			parameters[6].Value = model.Pd_Code;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Pd_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Kc_Pd1 ");
			strSql.Append(" where Pd_Code=@Pd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Pd_Code", SqlDbType.Char,9)			};
			parameters[0].Value = Pd_Code;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Pd_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Kc_Pd1 ");
			strSql.Append(" where Pd_Code in ("+Pd_Codelist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlKc_Pd1 GetModel(string Pd_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Pd_Code,Pd_Month,Czy_Code,Czy_Name,Pd_Date,Pd_Memo,Pd_Finish from Kc_Pd1 ");
			strSql.Append(" where Pd_Code=@Pd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Pd_Code", SqlDbType.Char,9)			};
			parameters[0].Value = Pd_Code;

			Model.MdlKc_Pd1 model=new Model.MdlKc_Pd1();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlKc_Pd1 DataRowToModel(DataRow row)
		{
			Model.MdlKc_Pd1 model=new Model.MdlKc_Pd1();
			if (row != null)
			{
				if(row["Pd_Code"]!=null)
				{
					model.Pd_Code=row["Pd_Code"].ToString();
				}
				if(row["Pd_Month"]!=null && row["Pd_Month"].ToString()!="")
				{
					model.Pd_Month=DateTime.Parse(row["Pd_Month"].ToString());
				}
				if(row["Czy_Code"]!=null)
				{
					model.Czy_Code=row["Czy_Code"].ToString();
				}
				if(row["Czy_Name"]!=null)
				{
					model.Czy_Name=row["Czy_Name"].ToString();
				}
				if(row["Pd_Date"]!=null && row["Pd_Date"].ToString()!="")
				{
					model.Pd_Date=DateTime.Parse(row["Pd_Date"].ToString());
				}
				if(row["Pd_Memo"]!=null)
				{
					model.Pd_Memo=row["Pd_Memo"].ToString();
				}
				if(row["Pd_Finish"]!=null && row["Pd_Finish"].ToString()!="")
				{
					if((row["Pd_Finish"].ToString()=="1")||(row["Pd_Finish"].ToString().ToLower()=="true"))
					{
						model.Pd_Finish=true;
					}
					else
					{
						model.Pd_Finish=false;
					}
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Pd_Code,Pd_Month,Czy_Code,Czy_Name,Pd_Date,Pd_Memo,Pd_Finish ");
			strSql.Append(" FROM Kc_Pd1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Pd_Code,Pd_Month,Czy_Code,Czy_Name,Pd_Date,Pd_Memo,Pd_Finish ");
			strSql.Append(" FROM Kc_Pd1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Kc_Pd1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Pd_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Kc_Pd1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Kc_Pd1";
			parameters[1].Value = "Pd_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod

		#region  ExtensionMethod


		#endregion  ExtensionMethod
	}
}

