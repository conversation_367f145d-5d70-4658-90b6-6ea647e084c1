﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{37A67F3B-713F-4D17-B970-837C70F898FE}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>药店管理系统</RootNamespace>
    <AssemblyName>药店管理系统</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>false</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>图标.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Controls, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CloudReg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CloudReg.designer.cs">
      <DependentUpon>CloudReg.cs</DependentUpon>
    </Compile>
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PublicFunction\DBUpdate.cs" />
    <Content Include="msvcr71.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="OSQL.EXE">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="图标.ico" />
    <Content Include="更新脚本\20250724.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250725.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250730.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250731.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250801.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250802.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250803.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250804.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250805.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\20250806.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="CloudReg.resx">
      <DependentUpon>CloudReg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Content Include="data\Conf.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Content Include="System.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Enum\Common.Enum.csproj">
      <Project>{eca72bf5-a6c2-4ecb-a80a-9723ef1098a1}</Project>
      <Name>Common.Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\DbProviderFactory\DbProviderFactory.csproj">
      <Project>{fdf5d6d6-d281-4884-a81a-d0c49c2f3bc7}</Project>
      <Name>DbProviderFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\DBUtility\DBUtility.csproj">
      <Project>{9aa4cca5-b6d1-4719-b1af-880910bbc87e}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdBaseDict\YdBaseDict.csproj">
      <Project>{896cd677-f8c6-4b85-8bd1-8cb657e18d62}</Project>
      <Name>YdBaseDict</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdBusiness\YdBusiness.csproj">
      <Project>{ca0ee4e7-b36c-4366-84ea-9f5de3d5c61e}</Project>
      <Name>YdBusiness</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdControl\YdControl.csproj">
      <Project>{186009e9-7a04-4519-a696-79e57bb80b4e}</Project>
      <Name>YdControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdEnum\YdEnum.csproj">
      <Project>{2658ea9e-035b-43e4-b40f-6cebe092f702}</Project>
      <Name>YdEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdGSP\YdGSP.csproj">
      <Project>{ad82e39b-996d-4075-932d-2e5b5a60731d}</Project>
      <Name>YdGSP</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdPara\YdPara.csproj">
      <Project>{3ba52857-0292-4d40-aef2-85b1c2eca673}</Project>
      <Name>YdPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdPublicFunction\YdPublicFunction.csproj">
      <Project>{dfd1bd9d-45ff-4998-99bf-2a661d038f7c}</Project>
      <Name>YdPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdQuery\YdQuery.csproj">
      <Project>{e4398c43-1723-4ee7-9857-38defac8a329}</Project>
      <Name>YdQuery</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdResources\YdResources.csproj">
      <Project>{fd356f66-2186-4b9d-b45b-e8e7b6040a8a}</Project>
      <Name>YdResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdSysManage\YdSysManage.csproj">
      <Project>{4334c0ec-0835-4231-a71d-81057565f5cf}</Project>
      <Name>YdSysManage</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdVar\YdVar.csproj">
      <Project>{4596a1b7-93c2-4ff7-9412-a9b49e7beb6a}</Project>
      <Name>YdVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="zh-CHS.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>