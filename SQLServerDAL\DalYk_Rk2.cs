﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_Rk2.cs
*
* 功 能： N/A
* 类 名： DalYk_Rk2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/8/1 9:27:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
    /// <summary>
    /// 数据访问类:DalYk_Rk2
    /// </summary>
    public partial class DalYk_Rk2 : IDalYk_Rk2
    {
        public DalYk_Rk2()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return Common.WinFormVar.Var.DbHelper.GetMaxID("Rk_Id", "Yk_Rk2");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int Rk_Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Yk_Rk2");
            strSql.Append(" where Rk_Id=@Rk_Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Id", SqlDbType.Int,4)
            };
            parameters[0].Value = Rk_Id;

            return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public int Add(Model.MdlYk_Rk2 model, ref string Rk_Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Yk_Rk2(");
            strSql.Append("Rk_Code,Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Rk_Sl,Rk_Dj,Rk_Money,Rk_Memo)");
            strSql.Append(" values (");
            strSql.Append("@Rk_Code,@Xl_Code,@Yp_Code,@Yp_Scph,@Yp_ScDate1,@Yp_ScDate2,@Rk_Sl,@Rk_Dj,@Rk_Money,@Rk_Memo)");
            strSql.Append(";select @Rk_Id=IDENT_CURRENT('Yk_Rk2')");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9),
                    new SqlParameter("@Xl_Code", SqlDbType.Char,8),
                    new SqlParameter("@Yp_Code", SqlDbType.Char,11),
                    new SqlParameter("@Yp_Scph", SqlDbType.VarChar,20),
                    new SqlParameter("@Yp_ScDate1", SqlDbType.SmallDateTime),
                    new SqlParameter("@Yp_ScDate2", SqlDbType.SmallDateTime),
                    new SqlParameter("@Rk_Sl", SqlDbType.Decimal,9),
                    new SqlParameter("@Rk_Dj", SqlDbType.Decimal,9),
                    new SqlParameter("@Rk_Money", SqlDbType.Decimal,9),
                    new SqlParameter("@Rk_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@Rk_Id", SqlDbType.Int)};
            parameters[0].Value = model.Rk_Code;
            parameters[1].Value = model.Xl_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.Yp_Code);
            parameters[3].Value = model.Yp_Scph;
            parameters[4].Value = model.Yp_ScDate1;
            parameters[5].Value = model.Yp_ScDate2;
            parameters[6].Value = model.Rk_Sl;
            parameters[7].Value = model.Rk_Dj;
            parameters[8].Value = model.Rk_Money;
            parameters[9].Value = model.Rk_Memo;
            parameters[10].Direction = ParameterDirection.Output;

          
            object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
            Rk_Id = parameters[10].Value + "";
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.MdlYk_Rk2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Yk_Rk2 set ");
            strSql.Append("Rk_Code=@Rk_Code,");
            strSql.Append("Xl_Code=@Xl_Code,");
            strSql.Append("Yp_Code=@Yp_Code,");
            strSql.Append("Yp_Scph=@Yp_Scph,");
            strSql.Append("Yp_ScDate1=@Yp_ScDate1,");
            strSql.Append("Yp_ScDate2=@Yp_ScDate2,");
            strSql.Append("Rk_Sl=@Rk_Sl,");
            strSql.Append("Rk_Dj=@Rk_Dj,");
            strSql.Append("Rk_Money=@Rk_Money,");
            strSql.Append("Rk_Memo=@Rk_Memo");
            strSql.Append(" where Rk_Id=@Rk_Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Code", SqlDbType.Char,9),
                    new SqlParameter("@Xl_Code", SqlDbType.Char,8),
                    new SqlParameter("@Yp_Code", SqlDbType.Char,11),
                    new SqlParameter("@Yp_Scph", SqlDbType.VarChar,20),
                    new SqlParameter("@Yp_ScDate1", SqlDbType.SmallDateTime),
                    new SqlParameter("@Yp_ScDate2", SqlDbType.SmallDateTime),
                    new SqlParameter("@Rk_Sl", SqlDbType.Decimal,9),
                    new SqlParameter("@Rk_Dj", SqlDbType.Decimal,9),
                    new SqlParameter("@Rk_Money", SqlDbType.Decimal,9),
                    new SqlParameter("@Rk_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@Rk_Id", SqlDbType.Int,4)};
            parameters[0].Value = model.Rk_Code;
            parameters[1].Value = model.Xl_Code;
            parameters[2].Value = model.Yp_Code;
            parameters[3].Value = model.Yp_Scph;
            parameters[4].Value = model.Yp_ScDate1;
            parameters[5].Value = model.Yp_ScDate2;
            parameters[6].Value = model.Rk_Sl;
            parameters[7].Value = model.Rk_Dj;
            parameters[8].Value = model.Rk_Money;
            parameters[9].Value = model.Rk_Memo;
            parameters[10].Value = model.Rk_Id;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int Rk_Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Yk_Rk2 ");
            strSql.Append(" where Rk_Id=@Rk_Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Id", SqlDbType.Int,4)
            };
            parameters[0].Value = Rk_Id;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Rk_Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Yk_Rk2 ");
            strSql.Append(" where Rk_Id in (" + Rk_Idlist + ")  ");
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlYk_Rk2 GetModel(int Rk_Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Rk_Id,Rk_Code,Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Rk_Sl,Rk_Dj,Rk_Money,Rk_Memo from Yk_Rk2 ");
            strSql.Append(" where Rk_Id=@Rk_Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Rk_Id", SqlDbType.Int,4)
            };
            parameters[0].Value = Rk_Id;

            Model.MdlYk_Rk2 model = new Model.MdlYk_Rk2();
            DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlYk_Rk2 DataRowToModel(DataRow row)
        {
            Model.MdlYk_Rk2 model = new Model.MdlYk_Rk2();
            if (row != null)
            {
                if (row["Rk_Id"] != null && row["Rk_Id"].ToString() != "")
                {
                    model.Rk_Id = int.Parse(row["Rk_Id"].ToString());
                }
                if (row["Rk_Code"] != null)
                {
                    model.Rk_Code = row["Rk_Code"].ToString();
                }
                if (row["Xl_Code"] != null)
                {
                    model.Xl_Code = row["Xl_Code"].ToString();
                }
                if (row["Yp_Code"] != null)
                {
                    model.Yp_Code = row["Yp_Code"].ToString();
                }
                if (row["Yp_Scph"] != null)
                {
                    model.Yp_Scph = row["Yp_Scph"].ToString();
                }
                if (row["Yp_ScDate1"] != null && row["Yp_ScDate1"].ToString() != "")
                {
                    model.Yp_ScDate1 = DateTime.Parse(row["Yp_ScDate1"].ToString());
                }
                if (row["Yp_ScDate2"] != null && row["Yp_ScDate2"].ToString() != "")
                {
                    model.Yp_ScDate2 = DateTime.Parse(row["Yp_ScDate2"].ToString());
                }
                if (row["Rk_Sl"] != null && row["Rk_Sl"].ToString() != "")
                {
                    model.Rk_Sl = decimal.Parse(row["Rk_Sl"].ToString());
                }
                if (row["Rk_Dj"] != null && row["Rk_Dj"].ToString() != "")
                {
                    model.Rk_Dj = decimal.Parse(row["Rk_Dj"].ToString());
                }
                if (row["Rk_Money"] != null && row["Rk_Money"].ToString() != "")
                {
                    model.Rk_Money = decimal.Parse(row["Rk_Money"].ToString());
                }
                if (row["Rk_Memo"] != null)
                {
                    model.Rk_Memo = row["Rk_Memo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Rk_Id,Rk_Code,Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Rk_Sl,Rk_Dj,Rk_Money,Rk_Memo ");
            strSql.Append(" FROM Yk_Rk2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Rk_Id,Rk_Code,Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Rk_Sl,Rk_Dj,Rk_Money,Rk_Memo ");
            strSql.Append(" FROM Yk_Rk2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Yk_Rk2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Rk_Id desc");
            }
            strSql.Append(")AS Row, T.*  from Yk_Rk2 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yk_Rk2";
			parameters[1].Value = "Rk_Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod

        public DataTable GetListByRkCode(string Rk_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Yp_Name,Yp_Pzwh,Yp_Bzgg,Yp_Scqy,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Yk_Rk2.Rk_Id,Rk_Money ");
            strSql.Append(",Rk_Sl,traccnnt,Rk_Dj,Yk_Rk2.Xl_Code,Yk_Rk2.Rk_Code,Rk_Memo,Yp_Zjgg,Yp_Bzzhb,Drug_Identification_Code,Yp_Code From Yk_Rk2  ");
            strSql.Append("left join Zd_Yp2 On Zd_Yp2.Xl_Code=Yk_Rk2.Xl_Code ");
            strSql.Append("left join (select Rk_Id,Count(1) as traccnnt From Yk_RkDrugtracinfo ");
            strSql.Append("group by Rk_Id)A on A.Rk_Id=Yk_Rk2.Rk_Id  ");

            strSql.Append($" Where Yk_Rk2.Rk_Code='{Rk_Code}'");

            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString()).Tables[0];
        }

        public bool UpdateYpCode(int RkId, string YpCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Yk_Rk2 set ");
            strSql.Append("Yp_Code=@Yp_Code ");
            strSql.Append(" where Rk_Id=@Rk_Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Yp_Code", SqlDbType.Char,11),
                    new SqlParameter("@Rk_Id", SqlDbType.Int,4)};

            parameters[0].Value = YpCode;
            parameters[1].Value = RkId;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public DataTable GetFirstYpDjList(string XlCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select top 1 Rk_Dj from Yk_Rk2  ");
            strSql.Append($"  Where Xl_Code='{XlCode}'");
            strSql.Append("  Order by Rk_Code desc");

            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString()).Tables[0];
        }

        #endregion  ExtensionMethod
    }
}

