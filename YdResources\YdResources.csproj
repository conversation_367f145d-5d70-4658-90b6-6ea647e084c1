﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FD356F66-2186-4B9D-B45B-E8E7B6040A8A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YdResources</RootNamespace>
    <AssemblyName>YdResources</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="C_Resources.cs" />
    <Compile Include="GridColImg.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GridColImg.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resource1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource1.resx</DependentUpon>
    </Compile>
    <Compile Include="StateRes.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>StateRes.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="GridColImg.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>GridColImg.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resource1.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="StateRes.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>StateRes.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退出系统.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\停用16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\启用16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\锁定16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\基础数据.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\系统设置.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\用户管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\角色管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\修改密码.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\默认16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\未完成.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\新单.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\已完成.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\完成16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\录入16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\已结束16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\信息16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\GSP管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\采购订单.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\采购管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\采购入库.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\采购验收.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\处方单位.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\存储条件.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\供应商.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\货架信息.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\交班记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\库存查询.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\库存管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\数据查询.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\特殊药品.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\温度湿度.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\系统参数设置.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\销售查询.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\销售出库.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\销售管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\销售记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药店信息.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品-调价.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品剂型.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品类别.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品-药品字典.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\医疗器械.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\支付方式.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\不合格药品锁定.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\不良反应记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\会员管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\人员培训计划及记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\人员体检计划及记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\设备检查记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\收银员交班.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\收银员交班记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\数据上传.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\温度湿度记录.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\系统参数.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品大类.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品调价.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\药品字典.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\消息查看.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\临期预警.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RX.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>