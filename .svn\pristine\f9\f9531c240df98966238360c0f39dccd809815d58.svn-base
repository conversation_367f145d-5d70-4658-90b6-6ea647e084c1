-- =============================================
-- 数据迁移脚本：Zd_Yp4 → Yk_Ck2 + Yk_CkDrugtracinfo
-- 创建日期: 2025/8/5
-- 功能说明: 将Zd_Yp4表的数据迁移到Yk_Ck2和Yk_CkDrugtracinfo表，并更新Yk_Ck1状态
-- 迁移逻辑:
--   1. 关联Zd_Yp4和Zd_Yp3表(通过Yp_Code)
--   2. 按Ck_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2分组
--   3. 每个分组的COUNT()作为出库数量插入Yk_Ck2
--   4. 每个分组的所有Sy_Code作为追溯码插入Yk_CkDrugtracinfo
--   5. 更新Yk_Ck1表中对应出库单的Ck_Ok字段为"已结算"
-- =============================================

USE Mis_Yd
GO

SET NOCOUNT ON
GO

BEGIN TRANSACTION
GO

PRINT '========================================='
PRINT '开始数据迁移：Zd_Yp4 → Yk_Ck2 + Yk_CkDrugtracinfo'
PRINT '执行时间：' + CONVERT(VARCHAR(20), GETDATE(), 120)
PRINT '========================================='

-- 检查源表数据
DECLARE @SourceCount INT
SELECT @SourceCount = COUNT(*) FROM Zd_Yp4 WHERE Ck_Code IS NOT NULL AND Ck_Code <> ''
PRINT '源表Zd_Yp4记录数（有出库单号）：' + CAST(@SourceCount AS VARCHAR(10))

-- 检查目标表是否存在
IF OBJECT_ID('dbo.Yk_Ck2', 'U') IS NULL
BEGIN
    PRINT '错误：目标表Yk_Ck2不存在，请先执行表结构创建脚本'
    ROLLBACK TRANSACTION
    RETURN
END

IF OBJECT_ID('dbo.Yk_CkDrugtracinfo', 'U') IS NULL
BEGIN
    PRINT '错误：目标表Yk_CkDrugtracinfo不存在，请先执行表结构创建脚本'
    ROLLBACK TRANSACTION
    RETURN
END

-- 创建临时表存储分组后的数据
IF OBJECT_ID('tempdb..#TempCk2Data') IS NOT NULL
    DROP TABLE #TempCk2Data

CREATE TABLE #TempCk2Data
(
    Ck_Code CHAR(9),
    Yp_Code CHAR(11),
    Yp_Scph VARCHAR(20),
    Yp_ScDate1 SMALLDATETIME,
    Yp_ScDate2 SMALLDATETIME,
    Ck_Sl NUMERIC(12, 4),
    Sy_Codes VARCHAR(MAX) -- 存储该分组的所有Sy_Code，用逗号分隔
)

PRINT '正在分析和分组数据...'

-- 插入分组数据到临时表
INSERT INTO #TempCk2Data (Ck_Code, Yp_Code, Yp_Scph, Yp_ScDate1, Yp_ScDate2, Ck_Sl, Sy_Codes)
SELECT 
    y4.Ck_Code,
    y4.Yp_Code,
    y3.Yp_Scph,
    y3.Yp_ScDate1,
    y3.Yp_ScDate2,
    COUNT(*) as Ck_Sl,
    STUFF((
        SELECT ',' + sy.Sy_Code
        FROM Zd_Yp4 sy
        WHERE sy.Ck_Code = y4.Ck_Code 
          AND sy.Yp_Code = y4.Yp_Code
          AND EXISTS (
              SELECT 1 FROM Zd_Yp3 sy3 
              WHERE sy3.Yp_Code = sy.Yp_Code 
                AND sy3.Yp_Scph = y3.Yp_Scph
                AND sy3.Yp_ScDate1 = y3.Yp_ScDate1
                AND sy3.Yp_ScDate2 = y3.Yp_ScDate2
          )
        FOR XML PATH('')
    ), 1, 1, '') as Sy_Codes
FROM Zd_Yp4 y4
INNER JOIN Zd_Yp3 y3 ON y4.Yp_Code = y3.Yp_Code
WHERE y4.Ck_Code IS NOT NULL AND y4.Ck_Code <> ''
GROUP BY y4.Ck_Code, y4.Yp_Code, y3.Yp_Scph, y3.Yp_ScDate1, y3.Yp_ScDate2

DECLARE @GroupCount INT = @@ROWCOUNT
PRINT '数据分组完成，共 ' + CAST(@GroupCount AS VARCHAR(10)) + ' 个分组'

-- 检查是否有数据需要迁移
IF @GroupCount = 0
BEGIN
    PRINT '警告：没有找到可迁移的数据（可能是Zd_Yp4中的Yp_Code在Zd_Yp3中找不到对应记录，或没有出库单号）'
    DROP TABLE #TempCk2Data
    ROLLBACK TRANSACTION
    RETURN
END

-- 创建临时表存储插入的Ck_Id和对应的Sy_Codes
IF OBJECT_ID('tempdb..#TempCkIds') IS NOT NULL
    DROP TABLE #TempCkIds

CREATE TABLE #TempCkIds
(
    Ck_Id INT,
    Ck_Code CHAR(9),
    Sy_Codes VARCHAR(MAX)
)

PRINT '开始插入Yk_Ck2表...'

-- 逐条插入Yk_Ck2表并记录生成的Ck_Id
DECLARE @Ck_Code CHAR(9), @Yp_Code CHAR(11)
DECLARE @Yp_Scph VARCHAR(20), @Yp_ScDate1 SMALLDATETIME, @Yp_ScDate2 SMALLDATETIME
DECLARE @Ck_Sl NUMERIC(12, 4), @Sy_Codes VARCHAR(MAX), @New_Ck_Id INT
DECLARE @InsertCount INT = 0

DECLARE cur_ck2 CURSOR FOR
SELECT Ck_Code, Yp_Code, Yp_Scph, Yp_ScDate1, Yp_ScDate2, Ck_Sl, Sy_Codes
FROM #TempCk2Data

OPEN cur_ck2
FETCH NEXT FROM cur_ck2 INTO @Ck_Code, @Yp_Code, @Yp_Scph, @Yp_ScDate1, @Yp_ScDate2, @Ck_Sl, @Sy_Codes

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 插入Yk_Ck2表
    INSERT INTO Yk_Ck2 (Ck_Code, Yp_Code, Ck_Sl, Ck_Dj, Ck_Money, Ck_Memo)
    VALUES (@Ck_Code, @Yp_Code, @Ck_Sl, 0, 0, NULL)
    
    -- 获取刚插入记录的Ck_Id
    SET @New_Ck_Id = SCOPE_IDENTITY()
    SET @InsertCount = @InsertCount + 1
    
    -- 记录Ck_Id和对应的Sy_Codes
    INSERT INTO #TempCkIds (Ck_Id, Ck_Code, Sy_Codes)
    VALUES (@New_Ck_Id, @Ck_Code, @Sy_Codes)
    
    -- 每100条记录显示一次进度
    IF @InsertCount % 100 = 0
        PRINT '已插入 ' + CAST(@InsertCount AS VARCHAR(10)) + ' 条记录到Yk_Ck2表'
    
    FETCH NEXT FROM cur_ck2 INTO @Ck_Code, @Yp_Code, @Yp_Scph, @Yp_ScDate1, @Yp_ScDate2, @Ck_Sl, @Sy_Codes
END

CLOSE cur_ck2
DEALLOCATE cur_ck2

PRINT 'Yk_Ck2表插入完成，共插入 ' + CAST(@InsertCount AS VARCHAR(10)) + ' 条记录'

PRINT '开始插入Yk_CkDrugtracinfo表...'

-- 插入Yk_CkDrugtracinfo表
DECLARE @Ck_Id INT, @Single_Sy_Code VARCHAR(20)
DECLARE @TracCount INT = 0

DECLARE cur_tracinfo CURSOR FOR
SELECT Ck_Id, Ck_Code, Sy_Codes FROM #TempCkIds

OPEN cur_tracinfo
FETCH NEXT FROM cur_tracinfo INTO @Ck_Id, @Ck_Code, @Sy_Codes

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 分割Sy_Codes字符串，逐个插入
    DECLARE @Pos INT = 1, @NextPos INT
    WHILE @Pos <= LEN(@Sy_Codes)
    BEGIN
        SET @NextPos = CHARINDEX(',', @Sy_Codes, @Pos)
        IF @NextPos = 0
            SET @NextPos = LEN(@Sy_Codes) + 1
            
        SET @Single_Sy_Code = SUBSTRING(@Sy_Codes, @Pos, @NextPos - @Pos)
        
        IF LEN(LTRIM(RTRIM(@Single_Sy_Code))) > 0
        BEGIN
            INSERT INTO Yk_CkDrugtracinfo (Ck_Id, Ck_Code, drug_trac_codg)
            VALUES (@Ck_Id, @Ck_Code, LTRIM(RTRIM(@Single_Sy_Code)))
            SET @TracCount = @TracCount + 1
        END
        
        SET @Pos = @NextPos + 1
    END
    
    FETCH NEXT FROM cur_tracinfo INTO @Ck_Id, @Ck_Code, @Sy_Codes
END

CLOSE cur_tracinfo
DEALLOCATE cur_tracinfo

PRINT 'Yk_CkDrugtracinfo表插入完成，共插入 ' + CAST(@TracCount AS VARCHAR(10)) + ' 条记录'

-- 清理临时表
DROP TABLE #TempCk2Data
DROP TABLE #TempCkIds

PRINT '临时表清理完成'

-- 更新Yk_Ck1表的Ck_Ok字段为"已结算"
PRINT '开始更新Yk_Ck1.Ck_Ok字段...'

DECLARE @UpdateCount INT = 0

-- 更新所有在Yk_Ck2中有对应记录的Yk_Ck1记录的状态为"已结算"
UPDATE Yk_Ck1
SET Ck_Ok = '已结算'
WHERE Ck_Code IN (
    SELECT DISTINCT Ck_Code
    FROM Yk_Ck2
)
AND (Ck_Ok IS NULL OR Ck_Ok = '未结算')

SET @UpdateCount = @@ROWCOUNT
PRINT 'Yk_Ck1.Ck_Ok字段更新完成，共更新 ' + CAST(@UpdateCount AS VARCHAR(10)) + ' 条记录'

-- 提交事务
COMMIT TRANSACTION

PRINT '========================================='
PRINT '数据迁移成功完成！'
PRINT '完成时间：' + CONVERT(VARCHAR(20), GETDATE(), 120)
PRINT '========================================='

-- 显示迁移结果统计
PRINT '=== 迁移结果统计 ==='
PRINT '源表Zd_Yp4记录数（有出库单号）：' + CAST(@SourceCount AS VARCHAR(10))
PRINT 'Yk_Ck2表总记录数：' + CAST((SELECT COUNT(*) FROM Yk_Ck2) AS VARCHAR(10))
PRINT 'Yk_CkDrugtracinfo表总记录数：' + CAST((SELECT COUNT(*) FROM Yk_CkDrugtracinfo) AS VARCHAR(10))
PRINT '本次迁移Yk_Ck2记录数：' + CAST(@InsertCount AS VARCHAR(10))
PRINT '本次迁移Yk_CkDrugtracinfo记录数：' + CAST(@TracCount AS VARCHAR(10))
PRINT '本次更新Yk_Ck1.Ck_Ok记录数：' + CAST(@UpdateCount AS VARCHAR(10))

-- 数据验证查询示例
PRINT '=== 数据验证查询示例 ==='
PRINT '-- 查看迁移的数据样例：'
PRINT 'SELECT TOP 5 * FROM Yk_Ck2 ORDER BY Ck_Id DESC'
PRINT 'SELECT TOP 5 * FROM Yk_CkDrugtracinfo ORDER BY Ck_Id DESC'

GO

SET NOCOUNT OFF
