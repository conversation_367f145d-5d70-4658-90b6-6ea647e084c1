﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Model;

namespace YdTraceCode
{
    public class TraceCodeCk
    {
        private static BllZd_Yp4 _bllZd_Yp4 = new BllZd_Yp4();
        private static BllZd_Yp3 _bllZd_Yp3 = new BllZd_Yp3();
        public static bool ScanTraceCode(string traceCode, DataTable YpTable, DataTable tracTable, string ck_Code)
        {
            if (string.IsNullOrEmpty(traceCode))
            {
                MessageBox.Show("追溯码不能为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (tracTable.AsEnumerable().Where(p => p.Field<string>("drug_trac_codg") == traceCode).Count() > 0)
            {
                MessageBox.Show("追溯码已存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            int cnt = _bllZd_Yp4.GetRecordCount($"Sy_Code='{traceCode}'");
            if (cnt == 0)
            {
                MessageBox.Show("追溯码未入库", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            cnt = _bllZd_Yp4.GetRecordCount($"Sy_Code='{traceCode}' and Ck_Code is not null");
            if (cnt > 0)
            {
                MessageBox.Show("追溯码已出库", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            MdlZd_Yp4 mdlZd_Yp4 = _bllZd_Yp4.GetModel(traceCode);

            // 获取药品详细信息
            DataSet ypInfoDs = _bllZd_Yp3.GetListWithYpInfo($"y3.Yp_Code='{mdlZd_Yp4.Yp_Code}'");
            if (ypInfoDs.Tables[0].Rows.Count == 0)
            {
                MessageBox.Show("未找到药品信息", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }

            DataRow ypInfoRow = ypInfoDs.Tables[0].Rows[0];
            string ypCode = mdlZd_Yp4.Yp_Code;
            decimal ypPrice = mdlZd_Yp4.Yp_Xsdj ?? 0;

            // 在YpTable中查找是否已有该药品
            DataRow[] existingRows = YpTable.Select($"Yp_Code='{ypCode}'");
            if (existingRows.Length > 0)
            {
                // 已存在，检查数量是否等于对应的追溯码条数
                DataRow existingRow = existingRows[0];
                int ckId = Convert.ToInt32(existingRow["Ck_Id"]);
                decimal currentSl = Convert.ToDecimal(existingRow["Ck_Sl"]);

                // 统计tracTable中对应Ck_Id的追溯码条数
                int tracCount = tracTable.AsEnumerable().Where(p => p.Field<int>("Ck_Id") == ckId).Count();

                if (currentSl == tracCount)
                {
                    // 数量等于追溯码条数，可以增加数量和金额
                    decimal currentMoney = Convert.ToDecimal(existingRow["Ck_Money"]);
                    existingRow["Ck_Sl"] = currentSl + 1;
                    existingRow["Ck_Money"] = currentMoney + ypPrice;
                    existingRow["traccnt"] = Convert.ToDecimal(existingRow["traccnt"]) + 1;
                }
                else
                {
                    // 只增加追溯码数量，不增加药品数量
                    existingRow["traccnt"] = Convert.ToDecimal(existingRow["traccnt"]) + 1;
                }
                // 否则只插入tracTable，不增加数量
            }
            else
            {
                // 不存在，新增一行
                DataRow newRow = YpTable.NewRow();
                newRow["Ck_Id"] = -(YpTable.Rows.Count + 1);
                newRow["Ck_Code"] = ck_Code ?? DateTime.Now.ToString("yyMMdd");
                newRow["Yp_Code"] = ypCode;
                newRow["Ck_Sl"] = 1;
                newRow["Ck_Dj"] = ypPrice;
                newRow["Ck_Money"] = ypPrice;
                newRow["traccnt"] = 1;
                newRow["Yp_Name"] = ypInfoRow["Yp_Name"].ToString();
                newRow["Yp_Pzwh"] = ypInfoRow["Yp_Pzwh"].ToString();
                newRow["Yp_Scqy"] = ypInfoRow["Yp_Scqy"].ToString();
                newRow["Yp_Bzgg"] = ypInfoRow["Yp_Bzgg"].ToString();
                newRow["Yp_Zjdw"] = ypInfoRow["Yp_Zjdw"].ToString();
                newRow["Yp_Jx"] = ypInfoRow["Yp_Jx"].ToString();
                newRow["Yp_Otc"] = ypInfoRow["Yp_Otc"].ToString();
                newRow["Ck_Memo"] = "";

                YpTable.Rows.Add(newRow);
            }

            // 在tracTable中添加追溯码记录
            DataRow tracRow = tracTable.NewRow();
            if (existingRows.Length > 0)
            {
                // 如果药品已存在，使用现有行的Ck_Id
                tracRow["Ck_Id"] = Convert.ToInt32(existingRows[0]["Ck_Id"]);
            }
            else
            {
                // 如果是新药品，使用新分配的Ck_Id
                tracRow["Ck_Id"] = -(YpTable.Rows.Count);
            }
            tracRow["Ck_Code"] = ck_Code ?? DateTime.Now.ToString("yyMMdd"); ;
            tracRow["drug_trac_codg"] = traceCode;
            tracTable.Rows.Add(tracRow);

            return true;
        }
    }
}
