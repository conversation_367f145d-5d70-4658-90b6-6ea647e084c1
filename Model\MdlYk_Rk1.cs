﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlYk_Rk1.cs
*
* 功 能： N/A
* 类 名： MdlYk_Rk1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlYk_Rk1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlYk_Rk1
	{
		public MdlYk_Rk1()
		{ }
		#region Model
		private DateTime? _rk_date;
		private string _rk_code;
		private string _czy_code;
		private string _czy_name;
		private string _rk_memo;
		private bool _sc_finish;
		private string _kh_code;
		private string _kh_name;
		private string _rk_ok;
		public decimal Rk_Money { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Rk_Date
		{
			set { _rk_date = value; }
			get { return _rk_date; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Rk_Code
		{
			set { _rk_code = value; }
			get { return _rk_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Czy_Code
		{
			set { _czy_code = value; }
			get { return _czy_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Czy_Name
		{
			set { _czy_name = value; }
			get { return _czy_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Rk_Memo
		{
			set { _rk_memo = value; }
			get { return _rk_memo; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Sc_Finish
		{
			set { _sc_finish = value; }
			get { return _sc_finish; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Kh_Code
		{
			set { _kh_code = value; }
			get { return _kh_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Kh_Name
		{
			set { _kh_name = value; }
			get { return _kh_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Rk_Ok
		{
			set { _rk_ok = value; }
			get { return _rk_ok; }
		}
		#endregion Model

	}
}

