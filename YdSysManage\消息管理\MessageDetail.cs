using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using YdPublicFunction.Mdl;

namespace YdSysManage
{
    public partial class MessageDetail : Common.BaseForm.BaseFather
    {
        private MdlGetMessageReadListOut _messageData;

        public MessageDetail()
        {
            InitializeComponent();
        }

        public MessageDetail(MdlGetMessageReadListOut messageData) : this()
        {
            _messageData = messageData;
        }

        private void MessageDetail_Load(object sender, EventArgs e)
        {
            if (_messageData != null)
            {
                LoadMessageData();
            }
        }

        private void LoadMessageData()
        {
            try
            {
                // 设置标题信息
                lblTitle.Text = _messageData.Title ?? "";

                // 设置HTML内容
                if (!string.IsNullOrEmpty(_messageData.MessageContent))
                {
                    wbContent.DocumentText = _messageData.MessageContent;
                }
                else
                {
                    wbContent.DocumentText = "<html><body></body></html>";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载消息数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}