﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Common;
using CustomControl;
using Newtonsoft.Json;
using YdPublicFunction.Mdl;

namespace YdPublicFunction
{
    public class CentralApi
    {
        /// <summary>
        /// 根据电话号码获取数据库连接信息
        /// </summary>
        /// <param name="mdlGetDbConfigByTeleIn">输入参数</param>
        /// <param name="msg">返回消息</param>
        /// <param name="mdlOutPara">输出参数</param>
        /// <returns>状态码</returns>
        public static int GetDbConfigByTele(MdlGetDbConfigByTeleIn mdlGetDbConfigByTeleIn, ref string msg, ref MdlGetDbConfigByTeleOut mdlOutPara)
        {
            int result;
            string action = "/api/Zd_Yd/GetDbConfigByTele";

            result = SendMsg<MdlGetDbConfigByTeleIn, MdlGetDbConfigByTeleOut>(action, HttpVerb.POST, mdlGetDbConfigByTeleIn, ref msg, ref mdlOutPara, "data");
            return result;
        }

        /// <summary>
        /// 获取消息读取列表（已读/未读）
        /// </summary>
        /// <param name="mdlGetMessageReadListIn">输入参数</param>
        /// <param name="msg">返回消息</param>
        /// <param name="mdlOutPara">输出参数</param>
        /// <returns>状态码</returns>
        public static int GetMessageReadList(MdlGetMessageReadListIn mdlGetMessageReadListIn, ref string msg, ref List<MdlGetMessageReadListOut> mdlOutPara)
        {
            int result;
            string action = "/api/Message/GetMessageReadList";

            result = SendMsg<MdlGetMessageReadListIn, List<MdlGetMessageReadListOut>>(action, HttpVerb.POST, mdlGetMessageReadListIn, ref msg, ref mdlOutPara, "list");
            return result;
        }

        /// <summary>
        /// 药店读消息接口
        /// </summary>
        /// <param name="mdlMessageReadIn">输入参数</param>
        /// <param name="msg">返回消息</param>
        /// <param name="mdlOutPara">输出参数</param>
        /// <returns>状态码</returns>
        public static int MessageRead(MdlMessageReadIn mdlMessageReadIn, ref string msg, ref MdlMessageReadOut mdlOutPara)
        {
            int result;
            string action = "/api/Message/MessageRead";

            result = SendMsg<MdlMessageReadIn, MdlMessageReadOut>(action, HttpVerb.POST, mdlMessageReadIn, ref msg, ref mdlOutPara, "data");
            return result;
        }

        /// <summary>
        /// 获取药店未读消息数量
        /// </summary>
        /// <param name="mdlGetUnReadCountByYdCodeIn">输入参数</param>
        /// <param name="msg">返回消息</param>
        /// <param name="mdlOutPara">输出参数</param>
        /// <returns>状态码</returns>
        public static int GetUnReadCountByYdCode(MdlGetUnReadCountByYdCodeIn mdlGetUnReadCountByYdCodeIn, ref string msg, ref MdlGetUnReadCountByYdCodeOut mdlOutPara)
        {
            int result;
            string action = "/api/Message/GetUnReadCountByYdCode";

            result = SendMsg<MdlGetUnReadCountByYdCodeIn, MdlGetUnReadCountByYdCodeOut>(action, HttpVerb.POST, mdlGetUnReadCountByYdCodeIn, ref msg, ref mdlOutPara, "data");
            return result;
        }

        /// <summary>
        /// 获取指定药店的未读消息数量
        /// </summary>
        /// <param name="ydCode">药店编码</param>
        /// <returns>未读消息数量，失败时返回-1</returns>
        public static int GetUnReadCount(string ydCode)
        {
            try
            {
                MdlGetUnReadCountByYdCodeIn inputModel = new MdlGetUnReadCountByYdCodeIn();
                inputModel.Yd_Code = ydCode;

                string msg = "";
                MdlGetUnReadCountByYdCodeOut outputModel = null;

                int result = GetUnReadCountByYdCode(inputModel, ref msg, ref outputModel);

                if (result == 0 && outputModel != null)
                {
                    return outputModel.NUM;
                }
                else
                {
                    return -1;
                }
            }
            catch
            {
                return -1;
            }
        }
        public static int SendMsg<T, Out>(string action, HttpVerb method, T mdlInput, ref string msg, ref Out mdlOutput, string dataLb)
        {
            string timestamp;
            string url = "http://103.89.220.138:7747";
            MdlOutPara<Out> mdlOutputpara = new MdlOutPara<Out>();

            RestClient restClient = new RestClient(url + action);
            restClient.ContentType = "Application/json";
            restClient.Method = method;
            restClient.Timeout = 1000 * 60 * 5; //超时改成5分钟
            timestamp = TimeHelp.GetTimeStamp(DateTime.Now, 10);
            restClient.PostData = JsonConvert.SerializeObject(mdlInput);

            string outAll = restClient.MakeRequest();

            if (restClient.StatusCode != HttpStatusCode.OK)
            {
                msg = restClient.MakeRequest();
                return -2;
            }
            else
            {
                mdlOutputpara = JsonConvert.DeserializeObject<MdlOutPara<Out>>(outAll);
                switch (dataLb)
                {

                    case "list":
                        mdlOutput = mdlOutputpara.list;
                        break;
                    case "data":
                        mdlOutput = mdlOutputpara.data;
                        break;
                    default:
                        break;
                }

                msg = mdlOutputpara.stateMsg;
                return Convert.ToInt32(mdlOutputpara.stateCode);

            }


        }
    }
}
