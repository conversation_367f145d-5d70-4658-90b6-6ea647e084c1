﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlYk_Ck1.cs
*
* 功 能： N/A
* 类 名： MdlYk_Ck1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlYk_Ck1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlYk_Ck1
	{
		public MdlYk_Ck1()
		{ }
		#region Model
		private string _yd_code;
		private DateTime _ck_date;
		private string _ck_code;
		private string _czy_code;
		private string _czy_name;
		private string _gk_name;
		private string _gk_tele;
		private string _gk_sfzh;
		private decimal? _ck_money;
		private decimal? _hy_zk = 1M;
		private decimal? _js_money;
		private string _ck_memo;
		private bool _ck_cf;
		private string _cf_code;
		private string _cf_cfys;
		private string _cf_dw;
		private string _cf_state = "2";
		private string _cf_memo;
		private DateTime? _sf_date;
		private string _sfys_code;
		private string _sfys_name;
		private string _sfys_memo;
		private bool _sc_finish;
		private decimal? _ck_sl;
		private string _pay_type;
		private byte[] _imagedata;
		private string _jb_code;
		private string _ck_ok;
		private string _prescriptionfilename;
		/// <summary>
		/// 
		/// </summary>
		public string Yd_Code
		{
			set { _yd_code = value; }
			get { return _yd_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime Ck_Date
		{
			set { _ck_date = value; }
			get { return _ck_date; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ck_Code
		{
			set { _ck_code = value; }
			get { return _ck_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Czy_Code
		{
			set { _czy_code = value; }
			get { return _czy_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Czy_Name
		{
			set { _czy_name = value; }
			get { return _czy_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Gk_Name
		{
			set { _gk_name = value; }
			get { return _gk_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Gk_Tele
		{
			set { _gk_tele = value; }
			get { return _gk_tele; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Gk_Sfzh
		{
			set { _gk_sfzh = value; }
			get { return _gk_sfzh; }
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Ck_Money
		{
			set { _ck_money = value; }
			get { return _ck_money; }
		}
		/// <summary>
		/// 折扣
		/// </summary>
		public decimal? Hy_Zk
		{
			set { _hy_zk = value; }
			get { return _hy_zk; }
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Js_Money
		{
			set { _js_money = value; }
			get { return _js_money; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ck_Memo
		{
			set { _ck_memo = value; }
			get { return _ck_memo; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Ck_Cf
		{
			set { _ck_cf = value; }
			get { return _ck_cf; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Cf_Code
		{
			set { _cf_code = value; }
			get { return _cf_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Cf_Cfys
		{
			set { _cf_cfys = value; }
			get { return _cf_cfys; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Cf_Dw
		{
			set { _cf_dw = value; }
			get { return _cf_dw; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Cf_State
		{
			set { _cf_state = value; }
			get { return _cf_state; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Cf_Memo
		{
			set { _cf_memo = value; }
			get { return _cf_memo; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Sf_Date
		{
			set { _sf_date = value; }
			get { return _sf_date; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Sfys_Code
		{
			set { _sfys_code = value; }
			get { return _sfys_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Sfys_Name
		{
			set { _sfys_name = value; }
			get { return _sfys_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Sfys_Memo
		{
			set { _sfys_memo = value; }
			get { return _sfys_memo; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Sc_Finish
		{
			set { _sc_finish = value; }
			get { return _sc_finish; }
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Ck_Sl
		{
			set { _ck_sl = value; }
			get { return _ck_sl; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Pay_Type
		{
			set { _pay_type = value; }
			get { return _pay_type; }
		}
		/// <summary>
		/// 
		/// </summary>
		public byte[] ImageData
		{
			set { _imagedata = value; }
			get { return _imagedata; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jb_Code
		{
			set { _jb_code = value; }
			get { return _jb_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ck_Ok
		{
			set { _ck_ok = value; }
			get { return _ck_ok; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string PrescriptionFileName
		{
			set { _prescriptionfilename = value; }
			get { return _prescriptionfilename; }
		}
		#endregion Model

	}
}

