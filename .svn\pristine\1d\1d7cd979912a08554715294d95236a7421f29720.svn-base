﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Common;
using Common.BaseForm;
using YdPublicFunction;

namespace YdBusiness
{
    public partial class Yf_Rk1 : Common.BaseForm.BaseDict1
    {
        private BLL.BllYk_Rk1 _bllYkRk1 = new BllYk_Rk1();
        public Yf_Rk1()
        {
            InitializeComponent();
        }

        private void Yf_Rk1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            FormInit();
            DataInit();
            myGrid1.Select();
        }
        #region 初始化
        private void FormInit()
        {
            doubleDateEdit1.CustomFormat = "yyyy-MM-dd";
            doubleDateEdit1.DisplayFormat = "yyyy-MM-dd";
            doubleDateEdit1.EditFormat = "yyyy-MM-dd";
            doubleDateEdit1.SelectedIndex = 5;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("完成状态", "Rk_Ok", 80, "中", "", false);
            myGrid1.Init_Column("入库编码", "Rk_Code", 120, "中", "", false);
            myGrid1.Init_Column("经销商", "Kh_Name", 280, "左", "", false);
            myGrid1.Init_Column("采购时间", "Rk_Date", 180, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("采购金额", "Rk_Money", 120, "右", "###,##0.00##", false);
            myGrid1.Init_Column("经手人", "Czy_Name", 120, "左", "", false);
            myGrid1.Init_Column("备注", "Rk_Memo", 65, "左", "", false);
            myGrid1.ColumnFooters = true;
            myGrid1.AllowSort = true;
            myGrid1.FetchRowStyles = true;
            myGrid1.FetchRowStyle += GridFunction.GridRkState_FetchRowStyle;
            myGrid1.Splits[0].DisplayColumns["Rk_Ok"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.RkState_FetchCellStyle;
            Task.Factory.StartNew(() =>
            {
                string wwc = _bllYkRk1.GetWwc(YdVar.Var.JsrCode);
                if (!wwc.IsNullOrEmpty()) CustomSunnyUI.MyMessageDialog.ShowWarningDialog($"您在以下日期有未完成的入库记录，请及时处理！\n{wwc}");
            });
        }
        #endregion

        #region 自定义函数
        private void DataInit()
        {
            base.MyTable = _bllYkRk1.GetList($"Rk_Date between '{DateTime.Parse(doubleDateEdit1.StartValue.ToString()).ToString("yyy-MM-dd")}' " +
                                             $"And '{DateTime.Parse(doubleDateEdit1.EndValue.ToString()).ToString("yyy-MM-dd 23:59:59")}' " +
                                             $"And Zd_Czy.Czy_Code='{YdVar.Var.JsrCode}'").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Rk_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = base.MyTable));
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Rk_Date desc";
            DataSum();
        }
        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            Yf_Rk2 vform = new Yf_Rk2(base.Insert, base.MyRow, base.MyTable);
            vform.Tag = base.MyRow["Rk_Code"];
            vform.ZbTransmitTxt = base.MyTransmitTxt;
            base.AddTabControl(vform, "药库采购入库明细-" + (base.MyRow["Kh_Name"].ToString() == "" ? "新单" : base.MyRow["Kh_Name"].ToString()), YdResources.C_Resources.GetImage16(""));
        }
        protected override void DataDelete()
        {
            if (myGrid1.Row + 1 > myGrid1.RowCount)
            {
                MessageBox.Show("请选择入库记录!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            //已经完成的不能删除
            if (_bllYkRk1.GetRecordCount($"Rk_Ok!='未完成' and Rk_Code='" + base.MyRow["Rk_Code"] + "'") > 0)
            {
                MessageBox.Show($"只有未完成的单据才能删除!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }

            if (MessageBox.Show("是否删除:药房采购入库【" + base.MyRow["Kh_Name"] + "】的未完成入库记录?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No) return;

            if (_bllYkRk1.DeleteAll(base.MyRow["Rk_Code"].ToString()) == true)
            {
                myGrid1.Delete();
                base.MyTable.AcceptChanges();
                DataSum();
                MessageBox.Show("数据删除成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
        }

        private void DataSum()
        {
            LblTotal.BeginInvoke(new Action(() => this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString()));
            decimal Rk_Money = MyView.ToTable().AsEnumerable().Where(p => p.Field<string>("Rk_Ok") == "已完成").Sum(p => p.Field<decimal>("Rk_Money"));
            myGrid1.Columns["Rk_Money"].FooterText = Rk_Money.ToString("###,##0.00##");
        }
        #endregion

        #region 事件
        private void Cmd_Add_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEdit(true);
        }
        private void Cmd_Del_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataDelete();
        }
        private void CmdQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataInit();
        }
        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            char[] split = { ' ' };
            string filter = "Kh_Name+Czy_Name";
            string strFilter = "";
            foreach (string substr in TxtFilter.Text.Replace("*", "[*]").Replace("%", "[%]").Split(split))
            {
                strFilter = strFilter + filter + " like '*" + substr + "*' And ";
            }
            strFilter = strFilter.Substring(0, strFilter.Length - 5);
            MyView.RowFilter = strFilter;
            DataSum();
        }
        #endregion


    }
}
