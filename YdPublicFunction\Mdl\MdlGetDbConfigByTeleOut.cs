using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace YdPublicFunction.Mdl
{
    /// <summary>
    /// 根据电话号码获取数据库连接信息响应模型
    /// </summary>
    public class MdlGetDbConfigByTeleOut
    {
        // /// <summary>
        // /// 数据库IP地址
        // /// </summary>
        // public string DatabaseIP { get; set; }

        // /// <summary>
        // /// 数据库用户名
        // /// </summary>
        // public string Username { get; set; }

        // /// <summary>
        // /// 数据库密码
        // /// </summary>
        // public string Password { get; set; }

        /// <summary>
        /// 数据库名称
        /// </summary>
        public string DatabaseName { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string Yd_Code { get; set; }

        /// <summary>
        /// 医院名称
        /// </summary>
        public string Yd_Name { get; set; }
    }
}