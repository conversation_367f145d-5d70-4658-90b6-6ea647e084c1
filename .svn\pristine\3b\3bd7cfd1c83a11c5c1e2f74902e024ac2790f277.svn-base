-- =============================================
-- 数据迁移脚本：Zd_Yp4 → Yk_Rk2 + Yk_RkDrugtracinfo
-- 创建日期: 2025/8/5
-- 功能说明: 将Zd_Yp4表的数据迁移到Yk_Rk2和Yk_RkDrugtracinfo表，并更新Yk_Rk1状态
-- 迁移逻辑:
--   1. 关联Zd_Yp4和Zd_Yp3表(通过Yp_Code)
--   2. 按Rk_Code,Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2分组
--   3. 每个分组的COUNT()作为入库数量插入Yk_Rk2
--   4. 每个分组的所有Sy_Code作为追溯码插入Yk_RkDrugtracinfo
--   5. 更新Yk_Rk1表中对应入库单的Rk_Ok字段为"已完成"
-- =============================================

USE Mis_Yd
GO

SET NOCOUNT ON
GO

BEGIN TRANSACTION
GO

PRINT '========================================='
PRINT '开始数据迁移：Zd_Yp4 → Yk_Rk2 + Yk_RkDrugtracinfo'
PRINT '执行时间：' + CONVERT(VARCHAR(20), GETDATE(), 120)
PRINT '========================================='

-- 检查源表数据
DECLARE @SourceCount INT
SELECT @SourceCount = COUNT(*) FROM Zd_Yp4
PRINT '源表Zd_Yp4记录数：' + CAST(@SourceCount AS VARCHAR(10))

-- 检查目标表是否存在
IF OBJECT_ID('dbo.Yk_Rk2', 'U') IS NULL
BEGIN
    PRINT '错误：目标表Yk_Rk2不存在，请先执行表结构创建脚本'
    ROLLBACK TRANSACTION
    RETURN
END

IF OBJECT_ID('dbo.Yk_RkDrugtracinfo', 'U') IS NULL
BEGIN
    PRINT '错误：目标表Yk_RkDrugtracinfo不存在，请先执行表结构创建脚本'
    ROLLBACK TRANSACTION
    RETURN
END

-- 创建临时表存储分组后的数据
IF OBJECT_ID('tempdb..#TempRk2Data') IS NOT NULL
    DROP TABLE #TempRk2Data

CREATE TABLE #TempRk2Data
(
    Rk_Code CHAR(9),
    Xl_Code CHAR(8),
    Yp_Code CHAR(11),
    Yp_Scph VARCHAR(20),
    Yp_ScDate1 SMALLDATETIME,
    Yp_ScDate2 SMALLDATETIME,
    Rk_Sl NUMERIC(12, 4),
    Sy_Codes VARCHAR(MAX) -- 存储该分组的所有Sy_Code，用逗号分隔
)

PRINT '正在分析和分组数据...'

-- 插入分组数据到临时表
INSERT INTO #TempRk2Data (Rk_Code, Xl_Code, Yp_Code, Yp_Scph, Yp_ScDate1, Yp_ScDate2, Rk_Sl, Sy_Codes)
SELECT 
    y4.Rk_Code,
    y3.Xl_Code,
    y4.Yp_Code,
    y3.Yp_Scph,
    y3.Yp_ScDate1,
    y3.Yp_ScDate2,
    COUNT(*) as Rk_Sl,
    STUFF((
        SELECT ',' + sy.Sy_Code
        FROM Zd_Yp4 sy
        WHERE sy.Rk_Code = y4.Rk_Code 
          AND sy.Yp_Code = y4.Yp_Code
          AND EXISTS (
              SELECT 1 FROM Zd_Yp3 sy3 
              WHERE sy3.Yp_Code = sy.Yp_Code 
                AND sy3.Yp_Scph = y3.Yp_Scph
                AND sy3.Yp_ScDate1 = y3.Yp_ScDate1
                AND sy3.Yp_ScDate2 = y3.Yp_ScDate2
          )
        FOR XML PATH('')
    ), 1, 1, '') as Sy_Codes
FROM Zd_Yp4 y4
INNER JOIN Zd_Yp3 y3 ON y4.Yp_Code = y3.Yp_Code
GROUP BY y4.Rk_Code, y3.Xl_Code, y4.Yp_Code, y3.Yp_Scph, y3.Yp_ScDate1, y3.Yp_ScDate2

DECLARE @GroupCount INT = @@ROWCOUNT
PRINT '数据分组完成，共 ' + CAST(@GroupCount AS VARCHAR(10)) + ' 个分组'

-- 检查是否有数据需要迁移
IF @GroupCount = 0
BEGIN
    PRINT '警告：没有找到可迁移的数据（可能是Zd_Yp4中的Yp_Code在Zd_Yp3中找不到对应记录）'
    DROP TABLE #TempRk2Data
    ROLLBACK TRANSACTION
    RETURN
END

-- 创建临时表存储插入的Rk_Id和对应的Sy_Codes
IF OBJECT_ID('tempdb..#TempRkIds') IS NOT NULL
    DROP TABLE #TempRkIds

CREATE TABLE #TempRkIds
(
    Rk_Id INT,
    Rk_Code CHAR(9),
    Sy_Codes VARCHAR(MAX)
)

PRINT '开始插入Yk_Rk2表...'

-- 逐条插入Yk_Rk2表并记录生成的Rk_Id
DECLARE @Rk_Code CHAR(9), @Xl_Code CHAR(8), @Yp_Code CHAR(11)
DECLARE @Yp_Scph VARCHAR(20), @Yp_ScDate1 SMALLDATETIME, @Yp_ScDate2 SMALLDATETIME
DECLARE @Rk_Sl NUMERIC(12, 4), @Sy_Codes VARCHAR(MAX), @New_Rk_Id INT
DECLARE @InsertCount INT = 0

DECLARE cur_rk2 CURSOR FOR
SELECT Rk_Code, Xl_Code, Yp_Code, Yp_Scph, Yp_ScDate1, Yp_ScDate2, Rk_Sl, Sy_Codes
FROM #TempRk2Data

OPEN cur_rk2
FETCH NEXT FROM cur_rk2 INTO @Rk_Code, @Xl_Code, @Yp_Code, @Yp_Scph, @Yp_ScDate1, @Yp_ScDate2, @Rk_Sl, @Sy_Codes

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 插入Yk_Rk2表
    INSERT INTO Yk_Rk2 (Rk_Code, Xl_Code, Yp_Code, Yp_Scph, Yp_ScDate1, Yp_ScDate2, Rk_Sl, Rk_Dj, Rk_Money, Rk_Memo)
    VALUES (@Rk_Code, @Xl_Code, @Yp_Code, @Yp_Scph, @Yp_ScDate1, @Yp_ScDate2, @Rk_Sl, 0, 0, NULL)
    
    -- 获取刚插入记录的Rk_Id
    SET @New_Rk_Id = SCOPE_IDENTITY()
    SET @InsertCount = @InsertCount + 1
    
    -- 记录Rk_Id和对应的Sy_Codes
    INSERT INTO #TempRkIds (Rk_Id, Rk_Code, Sy_Codes)
    VALUES (@New_Rk_Id, @Rk_Code, @Sy_Codes)
    
    -- 每100条记录显示一次进度
    IF @InsertCount % 100 = 0
        PRINT '已插入 ' + CAST(@InsertCount AS VARCHAR(10)) + ' 条记录到Yk_Rk2表'
    
    FETCH NEXT FROM cur_rk2 INTO @Rk_Code, @Xl_Code, @Yp_Code, @Yp_Scph, @Yp_ScDate1, @Yp_ScDate2, @Rk_Sl, @Sy_Codes
END

CLOSE cur_rk2
DEALLOCATE cur_rk2

PRINT 'Yk_Rk2表插入完成，共插入 ' + CAST(@InsertCount AS VARCHAR(10)) + ' 条记录'

PRINT '开始插入Yk_RkDrugtracinfo表...'

-- 插入Yk_RkDrugtracinfo表
DECLARE @Rk_Id INT, @Single_Sy_Code VARCHAR(20)
DECLARE @TracCount INT = 0

DECLARE cur_tracinfo CURSOR FOR
SELECT Rk_Id, Rk_Code, Sy_Codes FROM #TempRkIds

OPEN cur_tracinfo
FETCH NEXT FROM cur_tracinfo INTO @Rk_Id, @Rk_Code, @Sy_Codes

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 分割Sy_Codes字符串，逐个插入
    DECLARE @Pos INT = 1, @NextPos INT
    WHILE @Pos <= LEN(@Sy_Codes)
    BEGIN
        SET @NextPos = CHARINDEX(',', @Sy_Codes, @Pos)
        IF @NextPos = 0
            SET @NextPos = LEN(@Sy_Codes) + 1
            
        SET @Single_Sy_Code = SUBSTRING(@Sy_Codes, @Pos, @NextPos - @Pos)
        
        IF LEN(LTRIM(RTRIM(@Single_Sy_Code))) > 0
        BEGIN
            INSERT INTO Yk_RkDrugtracinfo (Rk_Id, Rk_Code, drug_trac_codg)
            VALUES (@Rk_Id, @Rk_Code, LTRIM(RTRIM(@Single_Sy_Code)))
            SET @TracCount = @TracCount + 1
        END
        
        SET @Pos = @NextPos + 1
    END
    
    FETCH NEXT FROM cur_tracinfo INTO @Rk_Id, @Rk_Code, @Sy_Codes
END

CLOSE cur_tracinfo
DEALLOCATE cur_tracinfo

PRINT 'Yk_RkDrugtracinfo表插入完成，共插入 ' + CAST(@TracCount AS VARCHAR(10)) + ' 条记录'

-- 清理临时表
DROP TABLE #TempRk2Data
DROP TABLE #TempRkIds

PRINT '临时表清理完成'

-- 更新Yk_Rk1表的Rk_Ok字段为"已完成"
PRINT '开始更新Yk_Rk1.Rk_Ok字段...'

DECLARE @UpdateCount INT = 0

-- 更新所有在Yk_Rk2中有对应记录的Yk_Rk1记录的状态为"已完成"
UPDATE Yk_Rk1
SET Rk_Ok = '已完成'
WHERE Rk_Code IN (
    SELECT DISTINCT Rk_Code
    FROM Yk_Rk2
)
AND (Rk_Ok IS NULL OR Rk_Ok = '未完成')

SET @UpdateCount = @@ROWCOUNT
PRINT 'Yk_Rk1.Rk_Ok字段更新完成，共更新 ' + CAST(@UpdateCount AS VARCHAR(10)) + ' 条记录'

-- 提交事务
COMMIT TRANSACTION

PRINT '========================================='
PRINT '数据迁移成功完成！'
PRINT '完成时间：' + CONVERT(VARCHAR(20), GETDATE(), 120)
PRINT '========================================='

-- 显示迁移结果统计
PRINT '=== 迁移结果统计 ==='
PRINT '源表Zd_Yp4记录数：' + CAST(@SourceCount AS VARCHAR(10))
PRINT 'Yk_Rk2表总记录数：' + CAST((SELECT COUNT(*) FROM Yk_Rk2) AS VARCHAR(10))
PRINT 'Yk_RkDrugtracinfo表总记录数：' + CAST((SELECT COUNT(*) FROM Yk_RkDrugtracinfo) AS VARCHAR(10))
PRINT '本次迁移Yk_Rk2记录数：' + CAST(@InsertCount AS VARCHAR(10))
PRINT '本次迁移Yk_RkDrugtracinfo记录数：' + CAST(@TracCount AS VARCHAR(10))
PRINT '本次更新Yk_Rk1.Rk_Ok记录数：' + CAST(@UpdateCount AS VARCHAR(10))

-- 数据验证查询示例
PRINT '=== 数据验证查询示例 ==='
PRINT '-- 查看迁移的数据样例：'
PRINT 'SELECT TOP 5 * FROM Yk_Rk2 ORDER BY Rk_Id DESC'
PRINT 'SELECT TOP 5 * FROM Yk_RkDrugtracinfo ORDER BY Rk_Id DESC'

GO

SET NOCOUNT OFF
