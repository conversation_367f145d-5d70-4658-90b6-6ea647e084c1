﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using C1.Win.C1TrueDBGrid;
using Common;

namespace YdPublicFunction
{
    public class GridFunction
    {
        //唐山医保状态使用
        public static void TsYbGrid_FetchRowStyle(object sender, FetchRowStyleEventArgs e)
        {
            Color color;
            string strFlag = ((C1TrueDBGrid)sender).Columns["isJs"].CellText(((C1TrueDBGrid)sender).RowBookmark(e.Row));
            switch (strFlag)
            {
                case "0":
                    color = Color.Wheat;
                    e.CellStyle.BackColor = color;
                    break;
                case "1":
                    color = Color.White;
                    e.CellStyle.BackColor = color;
                    break;
                case "2":
                    color = Color.LightGray;
                    e.CellStyle.BackColor = color;
                    break;
            }
        }



        public static void IsEnable_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            if (e.Column.Name == "启用" && new List<string> { "Status", "IsEnable", "KcEnable", "Kh_Use" }.Contains(e.Column.DataColumn.DataField))
            {
                string flag = grid.Columns[e.Column.DataColumn.DataField].CellValue(e.Row).ToString();
                if (flag == "True")
                {
                    e.CellStyle.ForegroundImage = YdResources.GridColImg.启用16;
                }
                else if (flag == "False")
                {
                    e.CellStyle.ForegroundImage = YdResources.GridColImg.停用16;
                }
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly;
            }
        }
        public static void IsLockedOut_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            if (e.Column.Name == "是否锁定" && new List<string> { "IsLockedOut" }.Contains(e.Column.DataColumn.DataField))
            {
                string flag = grid.Columns[e.Column.DataColumn.DataField].CellValue(e.Row).ToString();
                if (flag == "True")
                {
                    e.CellStyle.ForegroundImage = YdResources.GridColImg.锁定16;
                }
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly;
            }
        }
        public static void IsSh_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            if (new List<string> { "审核状态", "验收状态" }.Contains(e.Column.Name) && new List<string> { "Kh_Sh", "Dd_Finish" }.Contains(e.Column.DataColumn.DataField))
            {
                string flag = grid.Columns[e.Column.DataColumn.DataField].CellValue(e.Row).ToString();
                if (flag == "True")
                {
                    e.CellStyle.ForegroundImage = YdResources.GridColImg.默认16;
                }
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly;
            }
        }
        public static void IsOtc_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            if (new List<string> { "OTC" }.Contains(e.Column.Name) && new List<string> { "Yp_Otc" }.Contains(e.Column.DataColumn.DataField))
            {
                string flag = grid.Columns[e.Column.DataColumn.DataField].CellValue(e.Row).ToString();
                if (flag == "False")
                {
                    e.CellStyle.ForegroundImage = YdResources.GridColImg.RX;
                }
                else if (flag == "True")
                {
                    e.CellStyle.ForegroundImage = YdResources.GridColImg.OTC;
                }
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly;
            }
        }
        #region FetchRowStyle

        public static void Grid_FetchRowStyle(object sender, FetchRowStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            if (grid.Columns.IndexOf("Alter_Ts") > -1)
            {
                string str = grid[e.Row, "Alter_Ts"].ToString();
                int num;
                if (int.TryParse(str, out num))
                {
                    if (num <= 0)
                    {
                        e.CellStyle.BackColor = Color.FromArgb(251, 190, 206);
                    }
                    if (num <= 30 && Int32.Parse(str) > 0)
                    {
                        e.CellStyle.BackColor = Color.Orange;
                    }
                }

            }
        }

        public static void RkState_FetchCellStyle(object sender, C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;
            string strFlag = grid.Columns[e.Column.DataColumn.DataField].CellValue(e.Row).ToString();
            switch (e.Column.DataColumn.DataField)
            {
                case "Ck_Ok":
                case "Rk_Ok":
                case "Ck_Ok1":
                case "Tk_Ok1":
                    if (strFlag == "已结算")
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.完成16;
                    }
                    else
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.录入16;
                    }
                    break;
                case "Ck_Qr1":
                case "Tk_Qr1":
                    if (strFlag == "已接收")
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.已结束16;
                    }
                    else
                    {
                        e.CellStyle.ForegroundImage = YdResources.GridColImg.信息16;
                    }
                    break;
            }

            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.LeftOfText;
        }

        public static void GridRkState_FetchRowStyle(object sender, C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;

            if (grid.Columns.IndexOf("Rk_Ok") > -1
                || grid.Columns.IndexOf("Ck_Ok") > -1
                || grid.Columns.IndexOf("Ck_Ok1") > -1
                || grid.Columns.IndexOf("Tk_Ok") > -1
                || grid.Columns.IndexOf("Tk_Ok1") > -1
                || grid.Columns.IndexOf("Rk_State") > -1)
            {
                string str = "";
                if (grid.Columns.IndexOf("Ck_Ok") > -1)
                {
                    str = grid[e.Row, "Ck_Ok"].ToString();
                }
                else if (grid.Columns.IndexOf("Ck_Ok1") > -1)
                {
                    str = grid[e.Row, "Ck_Ok1"].ToString();
                }
                else if (grid.Columns.IndexOf("Tk_Ok") > -1)
                {
                    str = grid[e.Row, "Tk_Ok"].ToString();
                }
                else if (grid.Columns.IndexOf("Tk_Ok1") > -1)
                {
                    str = grid[e.Row, "Tk_Ok1"].ToString();
                }
                else if (grid.Columns.IndexOf("CgTk_State") > -1)
                {
                    str = grid[e.Row, "CgTk_State"].ToString();
                }
                else if (grid.Columns.IndexOf("Rk_Ok") > -1)
                {
                    str = grid[e.Row, "Rk_Ok"].ToString();
                }


                if (str != "已结算")
                {
                    e.CellStyle.BackColor = Color.Khaki;
                }
            }
        }
        public static void Mz_FetchRowStyle(object sender, C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs e)
        {
            C1TrueDBGrid grid = (C1TrueDBGrid)sender;

            if (grid.Columns.IndexOf("MzCf_Ok") > -1)
            {
                string str = "";
                str = grid[e.Row, "MzCf_Ok"].ToString();
                if (bool.Parse(str) == false)
                {
                    e.CellStyle.BackColor = Color.Khaki;
                }
            }
        }
        #endregion



    }


}

