-- =============================================
-- 初始化脚本 - 药店管理系统菜单数据
-- 创建日期: 2025/7/24
-- 说明: 根据药店管理系统界面初始化系统菜单数据
-- =============================================

USE Mis_Yd
GO

-- =============================================
-- 清空现有数据 - 按照外键关系顺序删除
-- =============================================

-- 2. 删除角色权限关系表数据
IF OBJECT_ID('dbo.SysRoleAuth', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysRoleAuth;
END

-- 3. 删除角色模块关系表数据
IF OBJECT_ID('dbo.SysRoleModule', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysRoleModule;
END

-- 4. 删除角色表数据
IF OBJECT_ID('dbo.SysRole', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysRole;
END

-- 5. 删除模块权限表数据
IF OBJECT_ID('dbo.SysModuleAuth', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysModuleAuth;
END

-- 6. 删除模块表数据
IF OBJECT_ID('dbo.SysModule', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysModule;
END

-- 7. 删除二级菜单表数据（如果存在）
IF OBJECT_ID('dbo.SysMenu2', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysMenu2;
END

-- 8. 删除一级菜单表数据
IF OBJECT_ID('dbo.SysMenu1', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysMenu1;
END

-- 药店管理系统不涉及医生表数据

PRINT '已清空相关表数据，准备重新初始化...'
GO

-- =============================================
-- 1. 插入一级菜单数据 (SysMenu1)
-- =============================================
INSERT INTO [dbo].[SysMenu1]
    ([FrsCode], [FrsName], [SortNo])
VALUES
    ('01', N'基础数据', 1),
    ('02', N'采购管理', 2),
    ('03', N'销售管理', 3),
    ('04', N'库存管理', 4),
    ('05', N'数据查询', 5),
    ('06', N'GSP管理', 6),
    ('07', N'系统设置', 7);

-- =============================================
-- 2. 插入二级菜单数据 (SysMenu2) - 与一级菜单相同
-- =============================================
INSERT INTO [dbo].[SysMenu2]
    ([ScndCode], [FrsCode], [ScndName], [SortNo])
VALUES
    ('01', '01', N'基础数据', 1),
    ('02', '02', N'采购管理', 1),
    ('03', '03', N'销售管理', 1),
    ('04', '04', N'库存管理', 1),
    ('05', '05', N'数据查询', 1),
    ('06', '06', N'GSP管理', 1),
    ('07', '07', N'系统设置', 1);

-- =============================================
-- 3. 插入模块数据 (SysModule)
-- =============================================

INSERT INTO [dbo].[SysModule]
    ([ScndCode], [ModuleCode], [ModuleName], [SortNo])
VALUES
    -- 基础数据模块
    ('01', '0001', N'药店信息', 1),
    ('01', '0002', N'供应商', 2),
    ('01', '0003', N'会员管理', 3),
    ('01', '0004', N'支付方式', 4),
    ('01', '0005', N'药品大类', 5),
    ('01', '0006', N'特殊药品', 6),
    ('01', '0007', N'药品剂型', 7),
    ('01', '0008', N'处方单位', 8),
    ('01', '0009', N'货架信息', 9),
    ('01', '0010', N'存储条件', 10),
    ('01', '0011', N'医疗器械', 11),

    -- 采购管理模块
    ('02', '0012', N'采购订单', 1),
    ('02', '0013', N'采购验收', 2),
    ('02', '0014', N'采购入库', 3),

    -- 销售管理模块
    ('03', '0015', N'销售出库', 1),
    ('03', '0016', N'销售记录', 2),
    ('03', '0017', N'处方管理', 3),
    ('03', '0018', N'销售查询', 4),
    ('03', '0019', N'收银员交班', 5),
    ('03', '0020', N'收银员交班记录', 6),

    -- 库存管理模块
    ('04', '0021', N'库存查询', 1),

    -- 数据查询模块
    ('05', '0022', N'药品字典', 1),
    ('05', '0023', N'药品调价', 2),
    ('05', '0024', N'数据上传', 3),

    -- GSP管理模块
    ('06', '0025', N'人员培训计划及记录', 1),
    ('06', '0026', N'人员体检计划及记录', 2),
    ('06', '0027', N'设备检查记录', 3),
    ('06', '0028', N'温度湿度记录', 4),
    ('06', '0029', N'不合格药品锁定', 5),
    ('06', '0030', N'不良反应记录', 6),

    -- 系统设置模块
    ('07', '0031', N'角色管理', 1),
    ('07', '0032', N'用户管理', 2),
    ('07', '0033', N'系统参数', 3);

-- =============================================
-- 验证插入结果
-- =============================================
PRINT '=== 一级菜单数据 ==='
SELECT *
FROM SysMenu1
ORDER BY SortNo;

PRINT '=== 二级菜单数据 ==='
SELECT *
FROM SysMenu2
ORDER BY FrsCode, SortNo;

PRINT '=== 模块数据 ==='
SELECT *
FROM SysModule
ORDER BY ScndCode, SortNo;

-- =============================================
-- 4. 插入角色数据 (SysRole)
-- =============================================
INSERT INTO [dbo].[SysRole]
    ([RoleCode], [RoleName], [RolePinYin], [RoleWuBi], [RoleMemo])
VALUES
    ('01', N'管理员', N'GLY', N'GLRY', N'系统管理员，拥有所有模块权限');

-- =============================================
-- 5. 插入角色模块权限 (SysRoleModule)
-- 为管理员角色分配所有模块权限
-- =============================================
INSERT INTO [dbo].[SysRoleModule]
    ([RoleCode], [ModuleCode])
SELECT '01', [ModuleCode]
FROM [dbo].[SysModule];

-- =============================================
-- 验证角色和权限数据
-- =============================================
PRINT '=== 角色数据 ==='
SELECT *
FROM SysRole;

PRINT '=== 角色模块权限数据 ==='
SELECT
    r.RoleName,
    m.ModuleName,
    rm.RoleCode,
    rm.ModuleCode
FROM SysRoleModule rm
    INNER JOIN SysRole r ON rm.RoleCode = r.RoleCode
    INNER JOIN SysModule m ON rm.ModuleCode = m.ModuleCode
ORDER BY rm.RoleCode, rm.ModuleCode;


UPDATE dbo.Zd_Czy SET RoleCode='01'

PRINT '初始化脚本执行完成！'