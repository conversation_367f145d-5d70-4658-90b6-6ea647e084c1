{"openapi": "3.0.1", "info": {"title": "接口文档", "version": "v1"}, "paths": {"/api/Auth/Login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInLogin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInLogin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInLogin"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInLogin"}}}}, "responses": {"200": {"description": "Success"}}}}, "/Common/UploadFile": {"post": {"tags": ["Common"], "summary": "存储文件", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"FileName": {"type": "string", "description": "自定文件名"}, "FileDir": {"type": "string", "description": "存储目录"}, "FileNameType": {"type": "integer", "description": "文件名生成类型 1 原文件名 2 自定义 3 自动生成", "format": "int32"}, "FileType": {"type": "string"}, "File": {"type": "string", "format": "binary"}}}, "encoding": {"FileName": {"style": "form"}, "FileDir": {"style": "form"}, "FileNameType": {"style": "form"}, "FileType": {"style": "form"}, "File": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/GetMessageList": {"post": {"tags": ["Message"], "summary": "获取消息列表", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/GetYdList": {"post": {"tags": ["Message"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/AddMessages": {"post": {"tags": ["Message"], "summary": "增加消息", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlAddMessagesIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlAddMessagesIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlAddMessagesIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlAddMessagesIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/UpdateMessages": {"post": {"tags": ["Message"], "summary": "更新消息", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessagesIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessagesIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessagesIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessagesIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/UploadPicture": {"post": {"tags": ["Message"], "summary": "通知里面上传图片接口", "responses": {"200": {"description": "Success"}}}}, "/api/Message/UpdateMessageStatus": {"post": {"tags": ["Message"], "summary": "更新通知状态接口", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessageStatusIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessageStatusIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessageStatusIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlUpdateMessageStatusIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/GetMessageReadList": {"post": {"tags": ["Message"], "summary": "已读 未读", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageReadListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageReadListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageReadListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetMessageReadListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/MessageRead": {"post": {"tags": ["Message"], "summary": "用于药店读消息时的接口", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlMessageReadIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlMessageReadIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlMessageReadIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlMessageReadIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Message/GetUnReadCountByYdCode": {"post": {"tags": ["Message"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetUnReadCountByYdCodeIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetUnReadCountByYdCodeIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetUnReadCountByYdCodeIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetUnReadCountByYdCodeIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListZd_Yd": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "药店总数", "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListKcZl": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "库存种类", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListKcZlXq": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "库存种类详情", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListCkZl": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "出库种类", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListCkZlXq": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "出库种类明细", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListCkSsql": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "实时数据", "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListCkxZl": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "出库种类 新", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kc"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListCkxZlXq": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "出库种类明细", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VMInZd_Yp4kcXq"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetListCkSsqlX": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "实时数据 新", "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetAdventPeriodList": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "药品临期预警--不分页", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetAdventPeriodListByPage": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "药品临期预警--分页", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListByPageIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListByPageIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListByPageIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetAdventPeriodListByPageIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpKindsCount": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetZxYpKindsCount": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetTopFiveSales": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取当月销售前五药品", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetTopFiveSalesIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetTopFiveSalesIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetTopFiveSalesIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetTopFiveSalesIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetPharmacyCount": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药店数量", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyCountIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyCountIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyCountIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyCountIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetOnSalesKindsCount": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取在售种类分布", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetOnSalesKindsCountIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetOnSalesKindsCountIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetOnSalesKindsCountIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetOnSalesKindsCountIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetSalesKindsCount": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取已售药品种类分布", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetSalesKindsCountIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetSalesKindsCountIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetSalesKindsCountIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetSalesKindsCountIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetNearExpirList": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "临期药品列表", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpirListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpirListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpirListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpirListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetDrugList": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetDrugDetaileList": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药店的药销售库存列表", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugDetaileListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugDetaileListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugDetaileListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetDrugDetaileListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetNearExpireListByPage": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取临期列表", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpireListByPageIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpireListByPageIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpireListByPageIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetNearExpireListByPageIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpKindsCountByPage": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountByPageIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountByPageIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountByPageIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKindsCountByPageIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpOutDetail": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药店出库明细", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpOutDetailIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpOutDetailIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpOutDetailIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpOutDetailIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpXsDetail": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药品销售明细", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpXsDetailIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpXsDetailIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpXsDetailIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpXsDetailIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetSupplierList": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取供应商列表", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetSupplierListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetSupplierListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetSupplierListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetSupplierListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYdListBySupplier": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取供应商提供的药店列表", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListBySupplierIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListBySupplierIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListBySupplierIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYdListBySupplierIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpDetatilBySupplierAndYd": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "根据 Yd_Code supplier_Code 获取药品种类明细 【分页】", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetatilBySupplierAndYdIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetatilBySupplierAndYdIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetatilBySupplierAndYdIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetatilBySupplierAndYdIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpKcDetailBySupplierYdXl": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "查询明细", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKcDetailBySupplierYdXlIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKcDetailBySupplierYdXlIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKcDetailBySupplierYdXlIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpKcDetailBySupplierYdXlIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpListBySupplier": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "查询供应商供应哪些药品", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListBySupplierIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListBySupplierIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListBySupplierIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListBySupplierIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYdDetailBySupplierAndYp": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYdDetailBySupplierAndYpIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYdDetailBySupplierAndYpIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYdDetailBySupplierAndYpIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYdDetailBySupplierAndYpIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpDetailBySupplierYdYp": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetailBySupplierYdYpIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetailBySupplierYdYpIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetailBySupplierYdYpIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpDetailBySupplierYdYpIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetYpList": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药品总种类", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetYpListIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetTraceCodeListByYp": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药品明细", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetTraceCodeListByYpIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetTraceCodeListByYpIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetTraceCodeListByYpIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetTraceCodeListByYpIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Mis_Yj/GetPharmacy": {"post": {"tags": ["<PERSON><PERSON>_<PERSON>j"], "summary": "获取药店列表--筛选 经纬坐标为空的不显示", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetPharmacyIn"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Zd_Yd/GetDbConfigByTele": {"post": {"tags": ["Zd_Yd"], "summary": "根据电话号码获取数据库连接信息", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/MdlGetDbConfigByTeleIn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MdlGetDbConfigByTeleIn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MdlGetDbConfigByTeleIn"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MdlGetDbConfigByTeleIn"}}}}, "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"MdlAddMessagesIn": {"required": ["isAllYd", "messageContent", "title"], "type": "object", "properties": {"title": {"minLength": 1, "type": "string"}, "messageContent": {"minLength": 1, "type": "string"}, "ydCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isAllYd": {"type": "boolean"}}, "additionalProperties": false}, "MdlGetAdventPeriodListByPageIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MdlGetAdventPeriodListIn": {"type": "object", "additionalProperties": false}, "MdlGetDbConfigByTeleIn": {"required": ["yd_Tele"], "type": "object", "properties": {"yd_Tele": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MdlGetDrugDetaileListIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "yd_Code": {"type": "string", "nullable": true}, "yp_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetDrugListIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "dl_Code": {"type": "string", "nullable": true}, "dl_Name": {"type": "string", "nullable": true}, "ydName": {"type": "string", "nullable": true}, "ypName": {"type": "string", "nullable": true}, "ypPh": {"type": "string", "nullable": true}, "syCode": {"type": "string", "nullable": true}, "yp_Status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetMessageListIn": {"required": ["isConstrain"], "type": "object", "properties": {"isConstrain": {"type": "boolean"}, "title": {"type": "string", "nullable": true}, "senderName": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MdlGetMessageReadListIn": {"type": "object", "properties": {"yd_Code": {"type": "string", "nullable": true}, "readStatus": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetNearExpirListIn": {"type": "object", "additionalProperties": false}, "MdlGetNearExpireListByPageIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "yd_Code": {"type": "string", "nullable": true}, "yp_Name": {"type": "string", "nullable": true}, "remainingDays": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "MdlGetOnSalesKindsCountIn": {"type": "object", "additionalProperties": false}, "MdlGetPharmacyCountIn": {"type": "object", "additionalProperties": false}, "MdlGetPharmacyIn": {"type": "object", "additionalProperties": false}, "MdlGetSalesKindsCountIn": {"type": "object", "additionalProperties": false}, "MdlGetSupplierListIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetTopFiveSalesIn": {"type": "object", "additionalProperties": false}, "MdlGetTraceCodeListByYpIn": {"required": ["ypBzgg", "ypName", "ypPzwh", "ypScqy", "ypZjgg"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "ypName": {"minLength": 1, "type": "string"}, "ypScqy": {"minLength": 1, "type": "string"}, "ypBzgg": {"minLength": 1, "type": "string"}, "ypZjgg": {"minLength": 1, "type": "string"}, "ypPzwh": {"minLength": 1, "type": "string"}, "ypScph": {"type": "string", "nullable": true}, "ypYxq1": {"type": "string", "nullable": true}, "ypYxq2": {"type": "string", "nullable": true}, "syCode": {"type": "string", "nullable": true}, "ydName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetUnReadCountByYdCodeIn": {"required": ["yd_Code"], "type": "object", "properties": {"yd_Code": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MdlGetYdDetailBySupplierAndYpIn": {"required": ["supplierCode", "yp_Bzgg", "yp_Name", "yp_Pzwh", "yp_Scqy", "yp_Zjgg"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierCode": {"minLength": 1, "type": "string"}, "yp_Name": {"minLength": 1, "type": "string"}, "yp_Bzgg": {"minLength": 1, "type": "string"}, "yp_Pzwh": {"minLength": 1, "type": "string"}, "yp_Zjgg": {"minLength": 1, "type": "string"}, "yp_Scqy": {"minLength": 1, "type": "string"}, "yd_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYdListBySupplierIn": {"required": ["supplierCode"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierCode": {"minLength": 1, "type": "string"}, "yd_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYdListIn": {"type": "object", "properties": {"yd_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpDetailBySupplierYdYpIn": {"required": ["supplierCode", "yd_Code", "yp_Bzgg", "yp_Name", "yp_Pzwh", "yp_Scqy", "yp_Zjgg"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierCode": {"minLength": 1, "type": "string"}, "yp_Name": {"minLength": 1, "type": "string"}, "yp_Bzgg": {"minLength": 1, "type": "string"}, "yp_Pzwh": {"minLength": 1, "type": "string"}, "yp_Zjgg": {"minLength": 1, "type": "string"}, "yp_Scqy": {"minLength": 1, "type": "string"}, "yd_Name": {"type": "string", "nullable": true}, "yd_Code": {"minLength": 1, "type": "string"}, "sy_Code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpDetatilBySupplierAndYdIn": {"required": ["supplierCode", "ydCode"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierCode": {"minLength": 1, "type": "string"}, "ydCode": {"minLength": 1, "type": "string"}, "yp_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpKcDetailBySupplierYdXlIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierCode": {"type": "string", "nullable": true}, "ydCode": {"type": "string", "nullable": true}, "xlCode": {"type": "string", "nullable": true}, "sy_Code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpKindsCountByPageIn": {"type": "object", "properties": {"sendername": {"type": "string", "nullable": true}, "startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MdlGetYpKindsCountIn": {"type": "object", "properties": {"sendername": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpListBySupplierIn": {"required": ["supplierCode"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "supplierCode": {"minLength": 1, "type": "string"}, "yd_Name": {"type": "string", "nullable": true}, "yp_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpListIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "yp_Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpOutDetailIn": {"required": ["xl_Code"], "type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "yd_Name": {"type": "string", "nullable": true}, "yd_Code": {"type": "string", "nullable": true}, "xl_Code": {"minLength": 1, "type": "string"}, "sy_Code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlGetYpXsDetailIn": {"type": "object", "properties": {"startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}, "yp_ScPh": {"type": "string", "nullable": true}, "sy_Code": {"type": "string", "nullable": true}, "yd_Name": {"type": "string", "nullable": true}, "startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MdlMessageReadIn": {"required": ["jsr_Code", "jsr_Name", "readID"], "type": "object", "properties": {"readID": {"type": "integer", "format": "int32"}, "jsr_Code": {"minLength": 1, "type": "string"}, "jsr_Name": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MdlUpdateMessageStatusIn": {"required": ["messageId", "status"], "type": "object", "properties": {"messageId": {"type": "integer", "format": "int32"}, "status": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MdlUpdateMessagesIn": {"required": ["isAllYd", "messageContent", "messageID", "title"], "type": "object", "properties": {"title": {"minLength": 1, "type": "string"}, "messageContent": {"minLength": 1, "type": "string"}, "ydCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isAllYd": {"type": "boolean"}, "messageID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "VMInLogin": {"required": ["login_Code", "login_PassWord"], "type": "object", "properties": {"login_Code": {"minLength": 1, "type": "string"}, "login_PassWord": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "VMInZd_Yp4kc": {"required": ["<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"sendername": {"minLength": 1, "type": "string"}, "startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "VMInZd_Yp4kcXq": {"type": "object", "properties": {"yd_Name": {"type": "string", "nullable": true}, "xl_Code": {"type": "string", "nullable": true}, "yp_Code": {"type": "string", "nullable": true}, "drugName": {"type": "string", "nullable": true}, "yp_Scph": {"type": "string", "nullable": true}, "startIndex": {"type": "integer", "format": "int32"}, "endIndex": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "在下框中输入请求头中需要添加Jwt授权Token：Bearer Token", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}